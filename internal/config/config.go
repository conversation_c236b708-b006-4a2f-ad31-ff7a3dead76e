// Package config handles loading and managing company configurations.
package config

import (
	"encoding/hex"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"secure_double_blind_ec/internal/types"
)

// LoadCompanyConfig loads a company's configuration from environment variables
func LoadCompanyConfig(companyID string) (*types.CompanyConfig, error) {
	var keyHex, saltHex string
	
	// Try company-specific environment variables first (for central services)
	switch companyID {
	case "company_a":
		keyHex = os.Getenv("COMPANY_A_KEY")
		saltHex = os.Getenv("COMPANY_A_SALT")
	case "company_b":
		keyHex = os.Getenv("COMPANY_B_KEY")
		saltHex = os.Getenv("COMPANY_B_SALT")
	case "company_c":
		keyHex = os.Getenv("COMPANY_C_KEY")
		saltHex = os.Getenv("COMPANY_C_SALT")
	}
	
	// If company-specific vars not found, try standard vars (for individual company services)
	if keyHex == "" || saltHex == "" {
		keyHex = os.Getenv("COMPANY_KEY")
		saltHex = os.Getenv("COMPANY_SALT")
	}
	
	if keyHex == "" || saltHex == "" {
		return nil, fmt.Errorf("missing environment variables COMPANY_KEY or COMPANY_SALT for company %s", companyID)
	}
	
	key, err := hex.DecodeString(keyHex)
	if err != nil {
		return nil, fmt.Errorf("invalid key format for company %s: %v", companyID, err)
	}
	
	salt, err := hex.DecodeString(saltHex)
	if err != nil {
		return nil, fmt.Errorf("invalid salt format for company %s: %v", companyID, err)
	}
	
	if len(key) < 32 || len(salt) < 32 {
		return nil, fmt.Errorf("key and salt must be at least 32 bytes for company %s", companyID)
	}
	
	return &types.CompanyConfig{
		ID:   companyID,
		Key:  key[:32], // Use first 32 bytes
		Salt: salt[:32], // Use first 32 bytes
	}, nil
}

// LoadAllCompanyConfigs loads configurations for all supported companies
func LoadAllCompanyConfigs() (map[string]*types.CompanyConfig, error) {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		return nil, fmt.Errorf("failed to load .env file: %v", err)
	}
	
	// Load all company configurations
	companies := make(map[string]*types.CompanyConfig)
	companyIDs := []string{"company_a", "company_b", "company_c"}
	
	for _, companyID := range companyIDs {
		config, err := LoadCompanyConfig(companyID)
		if err != nil {
			return nil, fmt.Errorf("failed to load config for %s: %v", companyID, err)
		}
		companies[companyID] = config
	}
	
	return companies, nil
}
