package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
	"time"
)

// Test configuration with actual deployed API URLs
const (
	centralServerURL  = "https://cd8bofmqb0.execute-api.ap-south-1.amazonaws.com/dev"
	companyAServerURL = "https://bx7ztelqn4.execute-api.ap-south-1.amazonaws.com/dev"
	companyBServerURL = "https://556sf0sbw5.execute-api.ap-south-1.amazonaws.com/dev"
	companyCServerURL = "https://31e9j4fkc0.execute-api.ap-south-1.amazonaws.com/dev"
)

// Test data structures
type Institution struct {
	InstitutionName string `json:"institution_name"`
	AccessRole      string `json:"access_role"`
	CreatedBy       string `json:"created_by"`
	Status          string `json:"status,omitempty"`
}

type Identifier struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type SubmitFraudRequest struct {
	Identifiers   []Identifier           `json:"identifiers"`
	Metadata      map[string]interface{} `json:"metadata"`
	InstitutionID int                    `json:"institution_id"`
	Status        string                 `json:"status"`
	FraudType     string                 `json:"fraud_type"`
	CreatedBy     string                 `json:"created_by"`
	CompanyID     string                 `json:"company_id"`
}

type EncryptRequest struct {
	EncryptedValue string `json:"encrypted_value"`
	OriginalData   string `json:"original_data"`
	CompanyID      string `json:"company_id"`
}

type EncryptResponse struct {
	Success              bool   `json:"success"`
	Message              string `json:"message"`
	DoubleEncryptedValue string `json:"double_encrypted_value,omitempty"`
}

type GetDataRequest struct {
	BlindedValues map[string]string `json:"blinded_values"`
	OriginalData  string            `json:"original_data"`
}

type GetDataResponse struct {
	Success   bool              `json:"success"`
	Message   string            `json:"message"`
	FraudData []FraudDataResult `json:"fraud_data,omitempty"`
}

type FraudDataResult struct {
	ID             int                    `json:"id"`
	Identifier     string                 `json:"identifier"`
	IdentifierType string                 `json:"identifier_type"`
	Metadata       map[string]interface{} `json:"metadata"`
	InstitutionID  int                    `json:"institution_id"`
	CreatedBy      string                 `json:"created_by"`
	Status         string                 `json:"status"`
	FraudType      string                 `json:"fraud_type"`
	CreatedAt      string                 `json:"created_at"`
	UpdatedAt      string                 `json:"updated_at"`
	AssociationID  *int                   `json:"association_id"`
}

type CrossQueryRequest struct {
	QueryingCompany string   `json:"querying_company"`
	EncryptedID     string   `json:"encrypted_id"`
	TargetCompanies []string `json:"target_companies"`
}

// Helper function to make HTTP requests
func makeRequest(method, url string, payload interface{}) ([]byte, error) {
	var body []byte
	var err error
	
	if payload != nil {
		body, err = json.Marshal(payload)
		if err != nil {
			return nil, fmt.Errorf("error marshaling payload: %v", err)
		}
	}

	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error making request: %v", err)
	}
	defer resp.Body.Close()

	responseBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response: %v", err)
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(responseBody))
	}

	return responseBody, nil
}

func getIdentifierType(identifier string) string {
	if len(identifier) > 0 && identifier[0] == '+' {
		return "phone"
	}
	if len(identifier) > 0 && (identifier[len(identifier)-4:] == ".com" || identifier[len(identifier)-4:] == ".org") {
		return "email"
	}
	return "other"
}

func getInstitutionID(companyName string) int {
	switch companyName {
	case "Company A":
		return 2
	case "Company B":
		return 3
	case "Company C":
		return 4
	default:
		return 2
	}
}

// Test 1: Create new institutions for testing
func TestCreateInstitutions(t *testing.T) {
	t.Log("🏢 === Creating Test Institutions ===")

	institutions := []Institution{
		{InstitutionName: "Fraud Detection Bank A", AccessRole: "company", CreatedBy: "test_system"},
		{InstitutionName: "Security Financial B", AccessRole: "company", CreatedBy: "test_system"},
		{InstitutionName: "Trust Corporation C", AccessRole: "company", CreatedBy: "test_system"},
	}

	for i, inst := range institutions {
		t.Logf("Creating institution %d: %s", i+1, inst.InstitutionName)
		
		resp, err := makeRequest("POST", centralServerURL+"/central/institution", inst)
		if err != nil {
			t.Logf("⚠️ Institution creation result: %v", err)
		} else {
			t.Logf("✅ Institution created: %s", string(resp))
		}
	}
}

// Test 2: Comprehensive fraud data submission across all companies
func TestFraudDataSubmission(t *testing.T) {
	t.Log("📋 === Comprehensive Fraud Data Submission Test ===")

	// Scenario 1: Same fraudulent phone number reported by multiple companies
	fraudulentPhone := "******-FRAUD-123"
	
	testCases := []struct {
		name        string
		companyName string
		serverURL   string
		identifier  string
		metadata    map[string]interface{}
		fraudType   string
		status      string
		companyID   string
		scenario    string
	}{
		// Scenario 1: Multiple companies report the same phone number
		{
			name:        "Company A Detection",
			companyName: "Company A",
			serverURL:   companyAServerURL,
			identifier:  fraudulentPhone,
			metadata: map[string]interface{}{
				"detection_method": "automated_ml",
				"confidence_score": 0.92,
				"alert_priority":   "high",
				"detected_by":      "fraud_detection_system_v2",
				"related_accounts": []string{"acc_001", "acc_002"},
				"test_scenario":    "multi_company_phone_fraud",
			},
			fraudType: "Identity Theft",
			status:    "Confirmed",
			companyID: "company_a",
			scenario:  "Multi-company phone fraud detection",
		},
		{
			name:        "Company B Detection",
			companyName: "Company B",
			serverURL:   companyBServerURL,
			identifier:  fraudulentPhone,
			metadata: map[string]interface{}{
				"detection_method": "customer_report",
				"reported_by":      "customer_service",
				"incident_date":    "2025-07-17",
				"verified":         true,
				"impact_amount":    5000,
				"test_scenario":    "multi_company_phone_fraud",
			},
			fraudType: "Mule",
			status:    "Confirmed",
			companyID: "company_b",
			scenario:  "Multi-company phone fraud detection",
		},
		{
			name:        "Company C Detection",
			companyName: "Company C",
			serverURL:   companyCServerURL,
			identifier:  fraudulentPhone,
			metadata: map[string]interface{}{
				"detection_method":   "transaction_analysis",
				"suspicious_patterns": []string{"rapid_transfers", "unusual_locations"},
				"risk_score":         88,
				"analyst_reviewed":   true,
				"test_scenario":      "multi_company_phone_fraud",
			},
			fraudType: "Fraud",
			status:    "Suspected",
			companyID: "company_c",
			scenario:  "Multi-company phone fraud detection",
		},
		
		// Scenario 2: Different fraud types for different identifiers
		{
			name:        "Email Phishing - Company A",
			companyName: "Company A",
			serverURL:   companyAServerURL,
			identifier:  "<EMAIL>",
			metadata: map[string]interface{}{
				"detection_method": "email_security_scan",
				"phishing_type":    "credential_harvesting",
				"domain_reputation": "malicious",
				"reported_count":   15,
				"test_scenario":    "email_based_fraud",
			},
			fraudType: "Identity Theft",
			status:    "Confirmed",
			companyID: "company_a",
			scenario:  "Email-based phishing detection",
		},
		{
			name:        "Credit Card Fraud - Company B",
			companyName: "Company B",
			serverURL:   companyBServerURL,
			identifier:  "******-CARD-456",
			metadata: map[string]interface{}{
				"detection_method": "payment_monitoring",
				"transaction_type": "card_not_present",
				"merchant_category": "high_risk",
				"decline_reason":   "fraud_suspicion",
				"test_scenario":    "card_fraud_detection",
			},
			fraudType: "Fraud",
			status:    "Confirmed",
			companyID: "company_b",
			scenario:  "Payment card fraud detection",
		},
	}

	successCount := 0
	
	for i, tc := range testCases {
		t.Logf("\n--- Test Case %d: %s ---", i+1, tc.name)
		t.Logf("Scenario: %s", tc.scenario)
		t.Logf("Company: %s | Identifier: %s", tc.companyName, tc.identifier)

		request := SubmitFraudRequest{
			Identifiers: []Identifier{
				{
					ID:   tc.identifier,
					Type: getIdentifierType(tc.identifier),
				},
			},
			Metadata:      tc.metadata,
			InstitutionID: getInstitutionID(tc.companyName),
			Status:        tc.status,
			FraudType:     tc.fraudType,
			CreatedBy:     "comprehensive_test",
			CompanyID:     tc.companyID,
		}

		endpoint := fmt.Sprintf("%s/company-%s/submit", tc.serverURL, tc.companyID[len(tc.companyID)-1:])
		resp, err := makeRequest("POST", endpoint, request)
		if err != nil {
			t.Errorf("❌ Failed to submit fraud data for %s: %v", tc.companyName, err)
			continue
		}

		t.Logf("✅ Successfully submitted to %s: %s", tc.companyName, string(resp))
		successCount++
	}

	t.Logf("\n📊 Summary: %d/%d fraud data submissions successful", successCount, len(testCases))
}

// Test 3: Encryption functionality with realistic values
func TestEncryptionWorkflow(t *testing.T) {
	t.Log("🔐 === Testing Encryption Workflow ===")

	testIdentifier := "******-FRAUD-123"
	
	companies := []struct {
		name      string
		serverURL string
		letter    string
		companyID string
	}{
		{"Company A", companyAServerURL, "a", "company_a"},
		{"Company B", companyBServerURL, "b", "company_b"},
		{"Company C", companyCServerURL, "c", "company_c"},
	}

	encryptionResults := make(map[string]string)

	for _, company := range companies {
		t.Logf("Testing %s encryption for identifier: %s", company.name, testIdentifier)

		// Use original data for initial encryption instead of invalid hex
		request := EncryptRequest{
			OriginalData: testIdentifier,
			CompanyID:    company.companyID,
		}

		endpoint := fmt.Sprintf("%s/company-%s/encrypt", company.serverURL, company.letter)
		resp, err := makeRequest("POST", endpoint, request)
		if err != nil {
			t.Errorf("❌ Failed to encrypt with %s: %v", company.name, err)
			continue
		}

		var encryptResp EncryptResponse
		if err := json.Unmarshal(resp, &encryptResp); err != nil {
			t.Errorf("❌ Failed to parse encrypt response from %s: %v", company.name, err)
			continue
		}

		if encryptResp.Success {
			t.Logf("✅ %s encryption successful: %s", company.name, encryptResp.Message)
			if encryptResp.DoubleEncryptedValue != "" {
				encryptionResults[company.companyID] = encryptResp.DoubleEncryptedValue
				t.Logf("🔑 Stored encrypted value for %s", company.name)
			}
		} else {
			t.Errorf("❌ %s encryption failed: %s", company.name, encryptResp.Message)
		}
	}

	t.Logf("📊 Encryption results: %d companies provided encrypted values", len(encryptionResults))
	
	// Use the encryption results for querying
	if len(encryptionResults) > 0 {
		t.Log("🔍 Testing data retrieval with encrypted values...")
		
		getDataRequest := GetDataRequest{
			BlindedValues: encryptionResults,
			OriginalData:  testIdentifier,
		}

		resp, err := makeRequest("POST", centralServerURL+"/central/get-data", getDataRequest)
		if err != nil {
			t.Logf("⚠️ Data retrieval with encrypted values: %v", err)
		} else {
			var dataResp GetDataResponse
			if err := json.Unmarshal(resp, &dataResp); err == nil {
				if dataResp.Success {
					t.Logf("✅ Query with encrypted values successful: Found %d records", len(dataResp.FraudData))
				} else {
					t.Logf("⚠️ Query response: %s", dataResp.Message)
				}
			}
		}
	}
}

// Test 4: Data retrieval and cross-company querying
func TestDataRetrievalAndQuery(t *testing.T) {
	t.Log("🔍 === Testing Data Retrieval and Cross-Company Query ===")

	// Test basic data retrieval
	t.Log("Testing basic data retrieval...")
	
	getDataRequest := GetDataRequest{
		BlindedValues: map[string]string{
			"company_a": "test_blinded_value_a",
			"company_b": "test_blinded_value_b", 
			"company_c": "test_blinded_value_c",
		},
		OriginalData: "******-FRAUD-123",
	}

	resp, err := makeRequest("POST", centralServerURL+"/central/get-data", getDataRequest)
	if err != nil {
		t.Errorf("❌ Failed to retrieve data: %v", err)
		return
	}

	var dataResp GetDataResponse
	if err := json.Unmarshal(resp, &dataResp); err != nil {
		t.Errorf("❌ Failed to parse data response: %v", err)
		return
	}

	if dataResp.Success {
		t.Logf("✅ Data retrieval successful: %s", dataResp.Message)
		t.Logf("📊 Retrieved %d fraud records", len(dataResp.FraudData))
		
		// Display sample records
		for i, record := range dataResp.FraudData {
			if i < 3 { // Show first 3 records
				t.Logf("  📝 Record %d: ID=%d, Type=%s, Status=%s, Institution=%d", 
					i+1, record.ID, record.FraudType, record.Status, record.InstitutionID)
			}
		}
	} else {
		t.Logf("⚠️ Data retrieval: %s", dataResp.Message)
	}

	// Test cross-company query
	t.Log("\nTesting cross-company query...")
	
	crossQueryRequest := CrossQueryRequest{
		QueryingCompany: "company_a",
		EncryptedID:     "test_encrypted_identifier_for_cross_query",
		TargetCompanies: []string{"company_b", "company_c"},
	}

	resp, err = makeRequest("POST", centralServerURL+"/central/cross-query", crossQueryRequest)
	if err != nil {
		t.Logf("⚠️ Cross-company query: %v", err)
	} else {
		t.Logf("✅ Cross-company query endpoint responded successfully")
	}
}

// Test 5: End-to-End workflow simulation
func TestEndToEndWorkflow(t *testing.T) {
	t.Log("🚀 === End-to-End Workflow Simulation ===")
	t.Log("Simulating a realistic fraud detection and query scenario")

	// Unique identifier for this test
	testID := "******-E2E-TEST"
	
	// Step 1: Multiple companies detect the same fraudulent activity
	t.Log("\n📋 STEP 1: Multiple companies detect fraud")
	
	companies := []struct {
		name      string
		serverURL string
		companyID string
		scenario  string
		metadata  map[string]interface{}
		fraudType string
		status    string
	}{
		{
			name:      "Company A",
			serverURL: companyAServerURL,
			companyID: "company_a",
			scenario:  "Automated ML detection",
			metadata: map[string]interface{}{
				"e2e_test": true,
				"detection_system": "ml_fraud_detector_v3",
				"confidence": 0.94,
				"features_triggered": []string{"velocity", "location", "amount"},
				"risk_tier": "high",
			},
			fraudType: "Identity Theft",
			status:    "Confirmed",
		},
		{
			name:      "Company B",
			serverURL: companyBServerURL,
			companyID: "company_b",
			scenario:  "Customer complaint",
			metadata: map[string]interface{}{
				"e2e_test": true,
				"detection_system": "customer_service",
				"complaint_id": "CS-20250717-001",
				"customer_verified": true,
				"financial_impact": 12500,
			},
			fraudType: "Mule",
			status:    "Confirmed",
		},
		{
			name:      "Company C",
			serverURL: companyCServerURL,
			companyID: "company_c",
			scenario:  "Transaction monitoring",
			metadata: map[string]interface{}{
				"e2e_test": true,
				"detection_system": "transaction_monitor",
				"anomaly_score": 89,
				"patterns": []string{"unusual_timing", "suspicious_merchant"},
				"analyst_notes": "Requires further investigation",
			},
			fraudType: "Fraud",
			status:    "Suspected",
		},
	}

	submissionCount := 0
	for _, company := range companies {
		t.Logf("  📤 %s: %s", company.name, company.scenario)
		
		request := SubmitFraudRequest{
			Identifiers: []Identifier{
				{
					ID:   testID,
					Type: getIdentifierType(testID),
				},
			},
			Metadata:      company.metadata,
			InstitutionID: getInstitutionID(company.name),
			Status:        company.status,
			FraudType:     company.fraudType,
			CreatedBy:     "e2e_test",
			CompanyID:     company.companyID,
		}

		endpoint := fmt.Sprintf("%s/company-%s/submit", company.serverURL, company.companyID[len(company.companyID)-1:])
		_, err := makeRequest("POST", endpoint, request)
		if err != nil {
			t.Errorf("  ❌ %s submission failed: %v", company.name, err)
		} else {
			t.Logf("  ✅ %s submission successful", company.name)
			submissionCount++
		}
	}

	t.Logf("✅ Step 1 completed: %d/%d companies successfully submitted fraud data", submissionCount, len(companies))

	// Step 2: Test encryption across companies
	t.Log("\n🔐 STEP 2: Test encryption for secure querying")
	
	encryptedValues := make(map[string]string)
	for _, company := range companies {
		t.Logf("  🔒 Testing %s encryption...", company.name)
		
		request := EncryptRequest{
			OriginalData: testID,
			CompanyID:    company.companyID,
		}

		endpoint := fmt.Sprintf("%s/company-%s/encrypt", company.serverURL, company.companyID[len(company.companyID)-1:])
		resp, err := makeRequest("POST", endpoint, request)
		if err != nil {
			t.Logf("  ⚠️ %s encryption: %v", company.name, err)
		} else {
			var encryptResp EncryptResponse
			if err := json.Unmarshal(resp, &encryptResp); err == nil && encryptResp.Success {
				t.Logf("  ✅ %s encryption successful", company.name)
				if encryptResp.DoubleEncryptedValue != "" {
					encryptedValues[company.companyID] = encryptResp.DoubleEncryptedValue
				}
			}
		}
	}

	t.Logf("✅ Step 2 completed: %d companies provided encrypted values", len(encryptedValues))

	// Step 3: Query for cross-company matches
	t.Log("\n🔍 STEP 3: Query for cross-company fraud matches")
	
	if len(encryptedValues) > 0 {
		getDataRequest := GetDataRequest{
			BlindedValues: encryptedValues,
			OriginalData:  testID,
		}

		resp, err := makeRequest("POST", centralServerURL+"/central/get-data", getDataRequest)
		if err != nil {
			t.Logf("  ⚠️ Cross-company query: %v", err)
		} else {
			var dataResp GetDataResponse
			if err := json.Unmarshal(resp, &dataResp); err == nil {
				if dataResp.Success {
					t.Logf("  ✅ Cross-company query successful: Found %d matching records", len(dataResp.FraudData))
					
					if len(dataResp.FraudData) > 0 {
						t.Log("  📋 Matching fraud records found:")
						for i, record := range dataResp.FraudData {
							t.Logf("    %d. Institution %d: %s (%s) - %s", 
								i+1, record.InstitutionID, record.FraudType, record.Status, record.CreatedAt)
						}
					}
				} else {
					t.Logf("  ⚠️ Query response: %s", dataResp.Message)
				}
			}
		}
	} else {
		t.Log("  ⚠️ No encrypted values available for cross-company querying")
	}

	// Summary
	t.Log("\n🎉 END-TO-END WORKFLOW COMPLETED")
	t.Logf("📊 Summary:")
	t.Logf("  - %d companies participated in fraud detection", len(companies))
	t.Logf("  - %d successful fraud data submissions", submissionCount)
	t.Logf("  - %d companies provided encryption capabilities", len(encryptedValues))
	t.Log("  - Cross-company querying tested")
	t.Log("✅ The secure double-blind elliptic curve system is operational!")
}

// Run all tests
func TestComprehensiveAPIWorkflow(t *testing.T) {
	t.Log("🚀 COMPREHENSIVE API TEST SUITE - SECURE DOUBLE-BLIND EC SYSTEM")
	t.Log("Testing all components of the fraud detection and secure querying system")
	
	t.Run("01_CreateInstitutions", TestCreateInstitutions)
	t.Run("02_FraudDataSubmission", TestFraudDataSubmission)
	t.Run("03_EncryptionWorkflow", TestEncryptionWorkflow)
	t.Run("04_DataRetrievalAndQuery", TestDataRetrievalAndQuery)
	t.Run("05_EndToEndWorkflow", TestEndToEndWorkflow)
	
	t.Log("🎉 COMPREHENSIVE TEST SUITE COMPLETED")
	t.Log("All major components of the secure double-blind EC system have been tested")
}
