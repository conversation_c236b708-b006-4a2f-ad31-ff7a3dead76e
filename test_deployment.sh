#!/bin/bash

# Simple test to verify the deployment is working
# This tests the basic endpoints without the database migration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Testing Deployment Status ===${NC}"

# API URLs from deployment
COMPANY_A_BASE_URL="https://bx7ztelqn4.execute-api.ap-south-1.amazonaws.com/dev"
COMPANY_B_BASE_URL="https://556sf0sbw5.execute-api.ap-south-1.amazonaws.com/dev"
COMPANY_C_BASE_URL="https://31e9j4fkc0.execute-api.ap-south-1.amazonaws.com/dev"
CENTRAL_BASE_URL="https://cd8bofmqb0.execute-api.ap-south-1.amazonaws.com/dev"

# Function to test endpoint
test_endpoint() {
    local url=$1
    local endpoint=$2
    local name=$3
    
    echo -e "${YELLOW}Testing $name - $endpoint${NC}"
    
    # Test with invalid/empty body to check if endpoint is reachable
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" -X POST "$url$endpoint" \
        -H "Content-Type: application/json" \
        -d '{}')
    
    # Extract HTTP status code
    http_code=$(echo $response | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    body=$(echo $response | sed -E 's/HTTPSTATUS:[0-9]*$//')
    
    if [ "$http_code" -eq "400" ] || [ "$http_code" -eq "200" ]; then
        echo -e "${GREEN}✓ $name endpoint is reachable (HTTP $http_code)${NC}"
    else
        echo -e "${RED}✗ $name endpoint failed (HTTP $http_code)${NC}"
        echo "Response: $body"
    fi
    
    echo ""
}

# Test all endpoints
echo -e "${YELLOW}Testing Company A endpoints...${NC}"
test_endpoint "$COMPANY_A_BASE_URL" "/company-a/query-async" "Company A Async Query"
test_endpoint "$COMPANY_A_BASE_URL" "/company-a/poll" "Company A Poll"
test_endpoint "$COMPANY_A_BASE_URL" "/company-a/query" "Company A Sync Query"
test_endpoint "$COMPANY_A_BASE_URL" "/company-a/submit" "Company A Submit"
test_endpoint "$COMPANY_A_BASE_URL" "/company-a/encrypt" "Company A Encrypt"

echo -e "${YELLOW}Testing Company B endpoints...${NC}"
test_endpoint "$COMPANY_B_BASE_URL" "/company-b/query-async" "Company B Async Query"
test_endpoint "$COMPANY_B_BASE_URL" "/company-b/poll" "Company B Poll"
test_endpoint "$COMPANY_B_BASE_URL" "/company-b/query" "Company B Sync Query"
test_endpoint "$COMPANY_B_BASE_URL" "/company-b/submit" "Company B Submit"
test_endpoint "$COMPANY_B_BASE_URL" "/company-b/encrypt" "Company B Encrypt"

echo -e "${YELLOW}Testing Company C endpoints...${NC}"
test_endpoint "$COMPANY_C_BASE_URL" "/company-c/query-async" "Company C Async Query"
test_endpoint "$COMPANY_C_BASE_URL" "/company-c/poll" "Company C Poll"
test_endpoint "$COMPANY_C_BASE_URL" "/company-c/query" "Company C Sync Query"
test_endpoint "$COMPANY_C_BASE_URL" "/company-c/submit" "Company C Submit"
test_endpoint "$COMPANY_C_BASE_URL" "/company-c/encrypt" "Company C Encrypt"

echo -e "${YELLOW}Testing Central Server endpoints...${NC}"
test_endpoint "$CENTRAL_BASE_URL" "/central/store" "Central Store"
test_endpoint "$CENTRAL_BASE_URL" "/central/cross-query" "Central Cross Query"
test_endpoint "$CENTRAL_BASE_URL" "/central/get-data" "Central Get Data"
test_endpoint "$CENTRAL_BASE_URL" "/central/institution" "Central Institution"

echo -e "${GREEN}=== Deployment Test Complete ===${NC}"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "1. Run database migration (see DATABASE_MIGRATION.md)"
echo "2. Test the full async query system with ./test_async_query.sh"
echo "3. Monitor CloudWatch logs for any issues"
