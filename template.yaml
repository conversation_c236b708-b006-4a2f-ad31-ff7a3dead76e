AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31

# Global configurations
Globals:
  Function:
    Timeout: 30
    MemorySize: 256
    Runtime: provided.al2023
    Architectures:
      - x86_64
    Environment:
      Variables:
        DB_HOST: !Ref DatabaseHost
        DB_PORT: !Ref DatabasePort
        DB_NAME: !Ref DatabaseName
        DB_USERNAME: !Ref DatabaseUsername
        DB_PASSWORD: !Ref DatabasePassword

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - staging
      - prod
    Description: Environment name

  DatabaseHost:
    Type: String
    Default: modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com
    Description: Existing PostgreSQL database host/endpoint
    
  DatabasePort:
    Type: String
    Default: "5432"
    Description: PostgreSQL database port

  DatabaseUsername:
    Type: String
    Default: postgres
    NoEcho: true
    Description: PostgreSQL username for existing database

  DatabasePassword:
    Type: String
    Default: "1rpraZEuRTp9aJjzFX47"
    NoEcho: true
    MinLength: 8
    Description: PostgreSQL password for existing database

  DatabaseName:
    Type: String
    Default: "consortium" 
    Description: PostgreSQL database name

  # Company Keys and Salts
  CompanyAKey:
    Type: String
    Default: "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"
    NoEcho: true
    MinLength: 64
    MaxLength: 64
    Description: 64-character hex key for Company A encryption

  CompanyASalt:
    Type: String
    Default: "fedcba9876543210fedcba9876543210fedcba9876543210fedcba9876543210"
    NoEcho: true
    MinLength: 64
    MaxLength: 64
    Description: 64-character hex salt for Company A encryption

  CompanyBKey:
    Type: String
    Default: "abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789"
    NoEcho: true
    MinLength: 64
    MaxLength: 64
    Description: 64-character hex key for Company B encryption

  CompanyBSalt:
    Type: String
    Default: "9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba"
    NoEcho: true
    MinLength: 64
    MaxLength: 64
    Description: 64-character hex salt for Company B encryption

  CompanyCKey:
    Type: String
    Default: "56789abcdef012356789abcdef012356789abcdef012356789abcdef01234567"
    NoEcho: true
    MinLength: 64
    MaxLength: 64
    Description: 64-character hex key for Company C encryption

  CompanyCSalt:
    Type: String
    Default: "210fedcba9876543210fedcba9876543210fedcba9876543210fedcba9876543"
    NoEcho: true
    MinLength: 64
    MaxLength: 64
    Description: 64-character hex salt for Company C encryption

Resources:
  # Lambda Functions

  # 1. Central Server - Store Fraud Data
  CentralStoreFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-store/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/store
            Method: post
            RestApiId: !Ref CentralServerApi

  # 2. Central Server - Create Institution
  CentralCreateInstitutionFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-create-institution/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/institution
            Method: post
            RestApiId: !Ref CentralServerApi

  # Company A Server Functions
  # 3. Company A Server - Submit Fraud Data
  CompanyASubmitFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-submit/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/submit
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 4. Company A Server - Encrypt Function
  CompanyAEncryptFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-encrypt/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/encrypt
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 5. Company A Server - Query ID
  CompanyAQueryFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/query
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # Company B Server Functions
  # 6. Company B Server - Submit Fraud Data
  CompanyBSubmitFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-submit/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/submit
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 7. Company B Server - Encrypt Function
  CompanyBEncryptFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-encrypt/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/encrypt
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 8. Company B Server - Query ID
  CompanyBQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/query
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # Company C Server Functions
  # 9. Company C Server - Submit Fraud Data
  CompanyCSubmitFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-submit/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/submit
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # 10. Company C Server - Encrypt Function
  CompanyCEncryptFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-encrypt/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/encrypt
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # 11. Company C Server - Query ID
  CompanyCQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/query
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # Async Query Functions
  # 12. Company A Server - Async Query
  CompanyAAsyncQueryFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query-async/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/query-async
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 13. Company B Server - Async Query
  CompanyBAsyncQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-async/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/query-async
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 14. Company C Server - Async Query
  CompanyCAsyncQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-async/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/query-async
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # Polling Functions
  # 15. Company A Server - Poll Query Status
  CompanyAPollFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query-poll/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/poll
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 16. Company B Server - Poll Query Status
  CompanyBPollFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query-poll/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/poll
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 17. Company C Server - Poll Query Status
  CompanyCPollFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/company-query-poll/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/poll
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # 6. Central Server - Cross Company Query
  CentralCrossQueryFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-cross-query/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/cross-query
            Method: post
            RestApiId: !Ref CentralServerApi

  # 7. Central Server - Get Data
  CentralGetDataFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-get-data/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/get-data
            Method: post
            RestApiId: !Ref CentralServerApi

  # Database Migration Function
  DatabaseMigrationFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/db-migration/
      Handler: bootstrap
      Timeout: 300

  # API Gateways
  CentralServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

  CompanyAServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

  CompanyBServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

  CompanyCServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"

Outputs:
  CentralServerApiUrl:
    Description: "API Gateway endpoint URL for Central Server"
    Value: !Sub "https://${CentralServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CentralServerApiUrl"

  CompanyAServerApiUrl:
    Description: "API Gateway endpoint URL for Company A Server"
    Value: !Sub "https://${CompanyAServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyAServerApiUrl"

  CompanyBServerApiUrl:
    Description: "API Gateway endpoint URL for Company B Server"
    Value: !Sub "https://${CompanyBServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyBServerApiUrl"

  CompanyCServerApiUrl:
    Description: "API Gateway endpoint URL for Company C Server"
    Value: !Sub "https://${CompanyCServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyCServerApiUrl"
