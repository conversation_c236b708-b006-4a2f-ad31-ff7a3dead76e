AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  secure-double-blind-ec
  
  SAM Template for Secure Double Blind Elliptic Curve system
  
  IMPORTANT: Universal Hash-to-Point Implementation
  ================================================
  
  This system uses UNIVERSAL hash-to-point for cross-company compatibility:
  - All companies use the same universal salt for hash-to-point operations
  - This ensures the same identifier maps to the same elliptic curve point across all companies
  - Critical for database queries: Company A's reverse-decrypted values will match Company B's stored values
  - Company-specific SALTS are used only for auxiliary operations, NOT for main encryption
  - Company-specific KEYS provide all the security through unique scalar generation
  
  Mathematical Property Verified: B(p) = A^(-1)(B(A(p)))
  Database Compatibility: Direct B(p) = Reverse-decrypted B(p)
  
  This enables secure cross-company fraud detection where companies can query
  each other's encrypted databases without compromising privacy.

  API ENDPOINTS AND SAMPLE REQUESTS/RESPONSES
  ===========================================

  CENTRAL SERVER ENDPOINTS:
  
  1. POST /central/store - Store fraud data from companies
     Sample Request:
     {
       "company_id": "company_a",
       "encrypted_data": {
         "phone": "04a1b2c3d4e5f6789...",
         "email": "048f7e6d5c4b3a21...",
         "account": "041a2b3c4d5e6f78..."
       },
       "timestamp": "2025-07-21T10:30:00Z"
     }
     Sample Response:
     {
       "success": true,
       "message": "Fraud data stored successfully",
       "record_id": "rec_**********"
     }

  2. POST /central/institution - Create new institution
     Sample Request:
     {
       "institution_name": "ABC Bank",
       "company_id": "company_a",
       "public_key": "04a1b2c3d4e5f6789..."
     }
     Sample Response:
     {
       "success": true,
       "message": "Institution created successfully",
       "institution_id": "inst_**********"
     }

  3. POST /central/cross-query - Cross-company query for fraud detection
     Sample Request:
     {
       "querying_company": "company_a",
       "target_companies": ["company_b", "company_c"],
       "encrypted_identifiers": {
         "phone": "04a1b2c3d4e5f6789...",
         "email": "048f7e6d5c4b3a21..."
       }
     }
     Sample Response:
     {
       "success": true,
       "results": [
         {
           "company_id": "company_b",
           "matches": [
             {
               "field": "phone",
               "confidence": 0.95,
               "timestamp": "2025-07-20T15:45:00Z"
             }
           ]
         }
       ]
     }

  4. POST /central/get-data - Retrieve stored fraud data
     Sample Request:
     {
       "company_id": "company_a",
       "record_ids": ["rec_**********", "rec_0987654321"]
     }
     Sample Response:
     {
       "success": true,
       "records": [
         {
           "record_id": "rec_**********",
           "encrypted_data": {
             "phone": "04a1b2c3d4e5f6789...",
             "email": "048f7e6d5c4b3a21..."
           },
           "timestamp": "2025-07-21T10:30:00Z"
         }
       ]
     }

  COMPANY ENDPOINTS (A, B, C):
  
  5. POST /company-{a,b,c}/submit - Submit fraud data to central server
     Sample Request:
     {
       "phone": "+**********",
       "email": "<EMAIL>",
       "account_number": "**********",
       "incident_type": "credit_card_fraud",
       "description": "Suspicious transaction pattern"
     }
     Sample Response:
     {
       "success": true,
       "message": "Fraud data submitted successfully",
       "encrypted_data": {
         "phone": "04a1b2c3d4e5f6789...",
         "email": "048f7e6d5c4b3a21...",
         "account": "041a2b3c4d5e6f78..."
       }
     }

  6. POST /company-{a,b,c}/encrypt - Encrypt identifiers for cross-company compatibility
     Sample Request:
     {
       "identifiers": {
         "phone": "+**********",
         "email": "<EMAIL>",
         "account_number": "**********"
       }
     }
     Sample Response:
     {
       "success": true,
       "encrypted_identifiers": {
         "phone": "04a1b2c3d4e5f6789...",
         "email": "048f7e6d5c4b3a21...",
         "account": "041a2b3c4d5e6f78..."
       }
     }

  7. POST /company-{a,b,c}/query - Query for potential fraud matches
     Sample Request:
     {
       "identifiers": {
         "phone": "+**********",
         "email": "<EMAIL>"
       },
       "target_companies": ["company_b", "company_c"]
     }
     Sample Response:
     {
       "success": true,
       "query_id": "query_**********",
       "matches": [
         {
           "company_id": "company_b",
           "field": "phone",
           "confidence": 0.95,
           "timestamp": "2025-07-20T15:45:00Z"
         }
       ]
     }

  8. POST /company-{a,b,c}/query-async - Asynchronous query for fraud matches
     Sample Request:
     {
       "identifiers": {
         "phone": "+**********",
         "email": "<EMAIL>"
       },
       "target_companies": ["company_b", "company_c"],
       "callback_url": "https://company-a.com/callback"
     }
     Sample Response:
     {
       "success": true,
       "query_id": "async_query_**********",
       "status": "processing",
       "estimated_completion": "2025-07-21T10:35:00Z"
     }

  9. POST /company-{a,b,c}/poll - Poll status of async query
     Sample Request:
     {
       "query_id": "async_query_**********"
     }
     Sample Response:
     {
       "success": true,
       "query_id": "async_query_**********",
       "status": "completed",
       "results": [
         {
           "company_id": "company_b",
           "matches": [
             {
               "field": "phone",
               "confidence": 0.95,
               "timestamp": "2025-07-20T15:45:00Z"
             }
           ]
         }
       ]
     }

  ERROR RESPONSES:
  ================
  
  Standard Error Format:
  {
    "success": false,
    "error": {
      "code": "INVALID_REQUEST",
      "message": "Missing required field: phone",
      "details": "The phone field is required for fraud detection queries"
    }
  }

  Common Error Codes:
  - INVALID_REQUEST: Malformed or missing required fields
  - ENCRYPTION_ERROR: Failed to encrypt/decrypt data
  - DATABASE_ERROR: Database operation failed
  - COMPANY_NOT_FOUND: Specified company does not exist
  - QUERY_NOT_FOUND: Query ID not found or expired
  - RATE_LIMIT_EXCEEDED: Too many requests from client

# Global configurations
Globals:
  Function:
    Timeout: 30
    MemorySize: 256
    Runtime: provided.al2023
    Architectures:
      - x86_64
    Environment:
      Variables:
        DB_HOST: !Ref DatabaseHost
        DB_PORT: !Ref DatabasePort
        DB_NAME: !Ref DatabaseName
        DB_USERNAME: !Ref DatabaseUsername
        DB_PASSWORD: !Ref DatabasePassword

Parameters:
  Environment:
    Type: String
    Default: dev
    AllowedValues:
      - dev
      - staging
      - prod
    Description: Environment name

  DatabaseHost:
    Type: String
    Default: modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com
    Description: Existing PostgreSQL database host/endpoint
    
  DatabasePort:
    Type: String
    Default: "5432"
    Description: PostgreSQL database port

  DatabaseUsername:
    Type: String
    Default: postgres
    NoEcho: true
    Description: PostgreSQL username for existing database

  DatabasePassword:
    Type: String
    Default: "1rpraZEuRTp9aJjzFX47"
    NoEcho: true
    MinLength: 8
    Description: PostgreSQL password for existing database

  DatabaseName:
    Type: String
    Default: "consortium" 
    Description: PostgreSQL database name

  # Company A Configuration
  CompanyAKey:
    Type: String
    NoEcho: true
    Description: 64-character hex key for Company A (used for scalar generation)
    Default: "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"

  CompanyASalt:
    Type: String
    NoEcho: true
    Description: 64-character hex salt for Company A (used for company-specific operations, NOT for hash-to-point)
    Default: "fedcba**********fedcba**********fedcba**********fedcba**********"

  # Company B Configuration
  CompanyBKey:
    Type: String
    NoEcho: true
    Description: 64-character hex key for Company B (used for scalar generation)
    Default: "abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789"

  CompanyBSalt:
    Type: String
    NoEcho: true
    Description: 64-character hex salt for Company B (used for company-specific operations, NOT for hash-to-point)
    Default: "**********fedcba**********fedcba**********fedcba**********fedcba"

  # Company C Configuration
  CompanyCKey:
    Type: String
    NoEcho: true
    Description: 64-character hex key for Company C (used for scalar generation)
    Default: "56789abcdef012356789abcdef012356789abcdef012356789abcdef0123456"

  CompanyCSalt:
    Type: String
    NoEcho: true
    Description: 64-character hex salt for Company C (used for company-specific operations, NOT for hash-to-point)
    Default: "210fedcba**********fedcba**********fedcba**********fedcba987654"





Resources:
  # Lambda Functions

  # 1. Central Server - Store Fraud Data
  CentralStoreFunction:
    Type: AWS::Serverless::Function
    Metadata:
      BuildMethod: makefile
    Properties:
      CodeUri: lambdas/central-store/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/store
            Method: post
            RestApiId: !Ref CentralServerApi

  # 2. Central Server - Create Institution
  CentralCreateInstitutionFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/central-create-institution/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/institution
            Method: post
            RestApiId: !Ref CentralServerApi

  # Company A Server Functions
  # 3. Company A Server - Submit Fraud Data
  CompanyASubmitFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-submit/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/submit
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 4. Company A Server - Encrypt Function
  CompanyAEncryptFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-encrypt/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/encrypt
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 5. Company A Server - Query ID
  CompanyAQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/query
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # Company B Server Functions
  # 6. Company B Server - Submit Fraud Data
  CompanyBSubmitFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-submit/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/submit
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 7. Company B Server - Encrypt Function
  CompanyBEncryptFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-encrypt/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/encrypt
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 8. Company B Server - Query ID
  CompanyBQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/query
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # Company C Server Functions
  # 9. Company C Server - Submit Fraud Data
  CompanyCSubmitFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-submit/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/submit
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # 10. Company C Server - Encrypt Function
  CompanyCEncryptFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-encrypt/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/encrypt
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # 11. Company C Server - Query ID
  CompanyCQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/query
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # Async Query Functions
  # 12. Company A Server - Async Query
  CompanyAAsyncQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-async/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/query-async
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 13. Company B Server - Async Query
  CompanyBAsyncQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-async/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/query-async
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 14. Company C Server - Async Query
  CompanyCAsyncQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-async/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/query-async
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # Polling Functions
  # 15. Company A Server - Poll Query Status
  CompanyAPollFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-poll/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_a"
          COMPANY_KEY: !Ref CompanyAKey
          COMPANY_SALT: !Ref CompanyASalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-a/poll
            Method: post
            RestApiId: !Ref CompanyAServerApi

  # 16. Company B Server - Poll Query Status
  CompanyBPollFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-poll/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_b"
          COMPANY_KEY: !Ref CompanyBKey
          COMPANY_SALT: !Ref CompanyBSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-b/poll
            Method: post
            RestApiId: !Ref CompanyBServerApi

  # 17. Company C Server - Poll Query Status
  CompanyCPollFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/company-query-poll/
      Handler: bootstrap
      Environment:
        Variables:
          COMPANY_ID: "company_c"
          COMPANY_KEY: !Ref CompanyCKey
          COMPANY_SALT: !Ref CompanyCSalt
      Events:
        Api:
          Type: Api
          Properties:
            Path: /company-c/poll
            Method: post
            RestApiId: !Ref CompanyCServerApi

  # 6. Central Server - Cross Company Query
  CentralCrossQueryFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/central-cross-query/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/cross-query
            Method: post
            RestApiId: !Ref CentralServerApi

  # 7. Central Server - Get Data
  CentralGetDataFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/central-get-data/
      Handler: bootstrap
      Events:
        Api:
          Type: Api
          Properties:
            Path: /central/get-data
            Method: post
            RestApiId: !Ref CentralServerApi

  # Database Migration Function
  DatabaseMigrationFunction:
    Type: AWS::Serverless::Function
    Properties:
      CodeUri: lambdas/db-migration/
      Handler: bootstrap
      Timeout: 300

  # API Gateways
  CentralServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      OpenApiVersion: 3.0.1
      DefinitionBody:
        openapi: 3.0.1
        info:
          title: "Central Server API - Secure Double Blind EC System"
          description: |
            Central server API for secure cross-company fraud detection using elliptic curve cryptography.
            
            ## Universal Hash-to-Point Implementation
            This system uses UNIVERSAL hash-to-point for cross-company compatibility:
            - All companies use the same universal salt for hash-to-point operations
            - Ensures same identifier maps to same elliptic curve point across all companies
            - Critical for database queries: Company A's reverse-decrypted values match Company B's stored values
            
            ## Mathematical Property
            B(p) = A^(-1)(B(A(p)))
            
            ## Database Compatibility
            Direct B(p) = Reverse-decrypted B(p)
          version: "1.0.0"
          contact:
            name: "Modus AI Consortium Team"
            email: "<EMAIL>"
        servers:
          - url: !Sub "https://${CentralServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
            description: "Central Server API"
        paths:
          /central/store:
            post:
              summary: "Store fraud data from companies"
              description: "Stores encrypted fraud data submitted by consortium companies"
              tags:
                - "Central Server"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - company_id
                        - encrypted_data
                      properties:
                        company_id:
                          type: string
                          enum: ["company_a", "company_b", "company_c"]
                          description: "ID of the submitting company"
                        encrypted_data:
                          type: object
                          properties:
                            phone:
                              type: string
                              description: "Encrypted phone number (elliptic curve point)"
                            email:
                              type: string
                              description: "Encrypted email address (elliptic curve point)"
                            account:
                              type: string
                              description: "Encrypted account number (elliptic curve point)"
                        timestamp:
                          type: string
                          format: date-time
                          description: "ISO 8601 timestamp of the incident"
                    example:
                      company_id: "company_a"
                      encrypted_data:
                        phone: "04a1b2c3d4e5f6789abcdef0123456789abcdef0123456789abcdef0123456789"
                        email: "048f7e6d5c4b3a210**********fedcba**********fedcba**********fedcba"
                        account: "041a2b3c4d5e6f789abcdef0123456789abcdef0123456789abcdef0123456789"
                      timestamp: "2025-07-21T10:30:00Z"
              responses:
                "200":
                  description: "Fraud data stored successfully"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          message:
                            type: string
                          record_id:
                            type: string
                      example:
                        success: true
                        message: "Fraud data stored successfully"
                        record_id: "rec_**********"
                "400":
                  description: "Invalid request"
                  content:
                    application/json:
                      schema:
                        $ref: "#/components/schemas/Error"
          /central/institution:
            post:
              summary: "Create new institution"
              description: "Registers a new financial institution in the consortium"
              tags:
                - "Central Server"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - institution_name
                        - company_id
                        - public_key
                      properties:
                        institution_name:
                          type: string
                          description: "Name of the financial institution"
                        company_id:
                          type: string
                          enum: ["company_a", "company_b", "company_c"]
                          description: "Company identifier"
                        public_key:
                          type: string
                          description: "Public key for the institution"
                    example:
                      institution_name: "ABC Bank"
                      company_id: "company_a"
                      public_key: "04a1b2c3d4e5f6789abcdef0123456789abcdef0123456789abcdef0123456789"
              responses:
                "200":
                  description: "Institution created successfully"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          message:
                            type: string
                          institution_id:
                            type: string
                      example:
                        success: true
                        message: "Institution created successfully"
                        institution_id: "inst_**********"
          /central/cross-query:
            post:
              summary: "Cross-company fraud detection query"
              description: "Performs fraud detection queries across multiple companies"
              tags:
                - "Central Server"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - querying_company
                        - target_companies
                        - encrypted_identifiers
                      properties:
                        querying_company:
                          type: string
                          enum: ["company_a", "company_b", "company_c"]
                        target_companies:
                          type: array
                          items:
                            type: string
                            enum: ["company_a", "company_b", "company_c"]
                        encrypted_identifiers:
                          type: object
                          properties:
                            phone:
                              type: string
                            email:
                              type: string
                            account:
                              type: string
                    example:
                      querying_company: "company_a"
                      target_companies: ["company_b", "company_c"]
                      encrypted_identifiers:
                        phone: "04a1b2c3d4e5f6789abcdef0123456789abcdef0123456789abcdef0123456789"
                        email: "048f7e6d5c4b3a210**********fedcba**********fedcba**********fedcba"
              responses:
                "200":
                  description: "Query results"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          results:
                            type: array
                            items:
                              type: object
                              properties:
                                company_id:
                                  type: string
                                matches:
                                  type: array
                                  items:
                                    type: object
                                    properties:
                                      field:
                                        type: string
                                      confidence:
                                        type: number
                                      timestamp:
                                        type: string
          /central/get-data:
            post:
              summary: "Retrieve stored fraud data"
              description: "Retrieves previously stored fraud data by record IDs"
              tags:
                - "Central Server"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - company_id
                        - record_ids
                      properties:
                        company_id:
                          type: string
                          enum: ["company_a", "company_b", "company_c"]
                        record_ids:
                          type: array
                          items:
                            type: string
                    example:
                      company_id: "company_a"
                      record_ids: ["rec_**********", "rec_0987654321"]
              responses:
                "200":
                  description: "Retrieved records"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          records:
                            type: array
                            items:
                              type: object
                              properties:
                                record_id:
                                  type: string
                                encrypted_data:
                                  type: object
                                timestamp:
                                  type: string
        components:
          schemas:
            Error:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                error:
                  type: object
                  properties:
                    code:
                      type: string
                      enum: ["INVALID_REQUEST", "ENCRYPTION_ERROR", "DATABASE_ERROR", "COMPANY_NOT_FOUND", "QUERY_NOT_FOUND", "RATE_LIMIT_EXCEEDED"]
                    message:
                      type: string
                    details:
                      type: string
              example:
                success: false
                error:
                  code: "INVALID_REQUEST"
                  message: "Missing required field: phone"
                  details: "The phone field is required for fraud detection queries"

  CompanyAServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      OpenApiVersion: 3.0.1
      DefinitionBody:
        openapi: 3.0.1
        info:
          title: "Company A Server API - Secure Double Blind EC System"
          description: |
            Company A server API for secure fraud detection and data encryption.
            
            ## Features
            - Submit fraud data to central consortium
            - Encrypt identifiers for cross-company compatibility
            - Query other companies for fraud matches
            - Asynchronous query processing with polling
          version: "1.0.0"
          contact:
            name: "Company A Fraud Detection Team"
            email: "<EMAIL>"
        servers:
          - url: !Sub "https://${CompanyAServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
            description: "Company A Server API"
        paths:
          /company-a/submit:
            post:
              summary: "Submit fraud data"
              description: "Submit fraud incident data to the central consortium"
              tags:
                - "Company A"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - phone
                        - email
                        - account_number
                        - incident_type
                      properties:
                        phone:
                          type: string
                          pattern: "^\\+?[1-9]\\d{1,14}$"
                          description: "Phone number in international format"
                        email:
                          type: string
                          format: email
                          description: "Email address of the fraudster"
                        account_number:
                          type: string
                          description: "Account number involved in fraud"
                        incident_type:
                          type: string
                          enum: ["credit_card_fraud", "identity_theft", "account_takeover", "synthetic_identity", "money_laundering"]
                          description: "Type of fraud incident"
                        description:
                          type: string
                          description: "Detailed description of the incident"
                    example:
                      phone: "+**********"
                      email: "<EMAIL>"
                      account_number: "**********"
                      incident_type: "credit_card_fraud"
                      description: "Suspicious transaction pattern detected"
              responses:
                "200":
                  description: "Fraud data submitted successfully"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          message:
                            type: string
                          encrypted_data:
                            type: object
                            properties:
                              phone:
                                type: string
                              email:
                                type: string
                              account:
                                type: string
                      example:
                        success: true
                        message: "Fraud data submitted successfully"
                        encrypted_data:
                          phone: "04a1b2c3d4e5f6789abcdef0123456789abcdef0123456789abcdef0123456789"
                          email: "048f7e6d5c4b3a210**********fedcba**********fedcba**********fedcba"
                          account: "041a2b3c4d5e6f789abcdef0123456789abcdef0123456789abcdef0123456789"
          /company-a/encrypt:
            post:
              summary: "Encrypt identifiers"
              description: "Encrypt personal identifiers for cross-company compatibility"
              tags:
                - "Company A"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - identifiers
                      properties:
                        identifiers:
                          type: object
                          properties:
                            phone:
                              type: string
                            email:
                              type: string
                            account_number:
                              type: string
                    example:
                      identifiers:
                        phone: "+**********"
                        email: "<EMAIL>"
                        account_number: "**********"
              responses:
                "200":
                  description: "Identifiers encrypted successfully"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          encrypted_identifiers:
                            type: object
          /company-a/query:
            post:
              summary: "Query for fraud matches"
              description: "Synchronous query for potential fraud matches across companies"
              tags:
                - "Company A"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - identifiers
                      properties:
                        identifiers:
                          type: object
                          properties:
                            phone:
                              type: string
                            email:
                              type: string
                            account_number:
                              type: string
                        target_companies:
                          type: array
                          items:
                            type: string
                            enum: ["company_b", "company_c"]
                    example:
                      identifiers:
                        phone: "+**********"
                        email: "<EMAIL>"
                      target_companies: ["company_b", "company_c"]
              responses:
                "200":
                  description: "Query results"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          query_id:
                            type: string
                          matches:
                            type: array
          /company-a/query-async:
            post:
              summary: "Asynchronous fraud query"
              description: "Start an asynchronous query for fraud matches"
              tags:
                - "Company A"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - identifiers
                      properties:
                        identifiers:
                          type: object
                        target_companies:
                          type: array
                          items:
                            type: string
                        callback_url:
                          type: string
                          format: uri
                    example:
                      identifiers:
                        phone: "+**********"
                        email: "<EMAIL>"
                      target_companies: ["company_b", "company_c"]
                      callback_url: "https://company-a.com/callback"
              responses:
                "200":
                  description: "Async query started"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          query_id:
                            type: string
                          status:
                            type: string
                          estimated_completion:
                            type: string
          /company-a/poll:
            post:
              summary: "Poll query status"
              description: "Check the status of an asynchronous query"
              tags:
                - "Company A"
              requestBody:
                required: true
                content:
                  application/json:
                    schema:
                      type: object
                      required:
                        - query_id
                      properties:
                        query_id:
                          type: string
                    example:
                      query_id: "async_query_**********"
              responses:
                "200":
                  description: "Query status"
                  content:
                    application/json:
                      schema:
                        type: object
                        properties:
                          success:
                            type: boolean
                          query_id:
                            type: string
                          status:
                            type: string
                          results:
                            type: array

  CompanyBServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      OpenApiVersion: 3.0.1
      DefinitionBody:
        openapi: 3.0.1
        info:
          title: "Company B Server API - Secure Double Blind EC System"
          description: "Company B server API for secure fraud detection and data encryption"
          version: "1.0.0"
        servers:
          - url: !Sub "https://${CompanyBServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
        paths:
          /company-b/submit:
            post:
              summary: "Submit fraud data"
              tags: ["Company B"]
              requestBody:
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/FraudSubmission"
              responses:
                "200":
                  description: "Success"
                  content:
                    application/json:
                      schema:
                        $ref: "#/components/schemas/SubmitResponse"
          /company-b/encrypt:
            post:
              summary: "Encrypt identifiers"
              tags: ["Company B"]
              requestBody:
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/EncryptRequest"
              responses:
                "200":
                  description: "Success"
          /company-b/query:
            post:
              summary: "Query for fraud matches"
              tags: ["Company B"]
              requestBody:
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/QueryRequest"
              responses:
                "200":
                  description: "Success"
          /company-b/query-async:
            post:
              summary: "Asynchronous fraud query"
              tags: ["Company B"]
              responses:
                "200":
                  description: "Success"
          /company-b/poll:
            post:
              summary: "Poll query status"
              tags: ["Company B"]
              responses:
                "200":
                  description: "Success"
        components:
          schemas:
            FraudSubmission:
              type: object
              required: ["phone", "email", "account_number", "incident_type"]
              properties:
                phone:
                  type: string
                email:
                  type: string
                account_number:
                  type: string
                incident_type:
                  type: string
                description:
                  type: string
            SubmitResponse:
              type: object
              properties:
                success:
                  type: boolean
                message:
                  type: string
                encrypted_data:
                  type: object
            EncryptRequest:
              type: object
              properties:
                identifiers:
                  type: object
            QueryRequest:
              type: object
              properties:
                identifiers:
                  type: object
                target_companies:
                  type: array
                  items:
                    type: string

  CompanyCServerApi:
    Type: AWS::Serverless::Api
    Properties:
      StageName: !Ref Environment
      Cors:
        AllowMethods: "'GET,POST,OPTIONS'"
        AllowHeaders: "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
        AllowOrigin: "'*'"
      OpenApiVersion: 3.0.1
      DefinitionBody:
        openapi: 3.0.1
        info:
          title: "Company C Server API - Secure Double Blind EC System"
          description: "Company C server API for secure fraud detection and data encryption"
          version: "1.0.0"
        servers:
          - url: !Sub "https://${CompanyCServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}"
        paths:
          /company-c/submit:
            post:
              summary: "Submit fraud data"
              tags: ["Company C"]
              requestBody:
                content:
                  application/json:
                    schema:
                      $ref: "#/components/schemas/FraudSubmission"
              responses:
                "200":
                  description: "Success"
          /company-c/encrypt:
            post:
              summary: "Encrypt identifiers"
              tags: ["Company C"]
              responses:
                "200":
                  description: "Success"
          /company-c/query:
            post:
              summary: "Query for fraud matches"
              tags: ["Company C"]
              responses:
                "200":
                  description: "Success"
          /company-c/query-async:
            post:
              summary: "Asynchronous fraud query"
              tags: ["Company C"]
              responses:
                "200":
                  description: "Success"
          /company-c/poll:
            post:
              summary: "Poll query status"
              tags: ["Company C"]
              responses:
                "200":
                  description: "Success"
        components:
          schemas:
            FraudSubmission:
              type: object
              required: ["phone", "email", "account_number", "incident_type"]
              properties:
                phone:
                  type: string
                email:
                  type: string
                account_number:
                  type: string
                incident_type:
                  type: string

  # Custom Resource to run database migration - commented out for manual setup
  # DatabaseMigration:
  #   Type: AWS::CloudFormation::CustomResource
  #   Properties:
  #     ServiceToken: !GetAtt DatabaseMigrationFunction.Arn

Outputs:
  CentralServerApiUrl:
    Description: "API Gateway endpoint URL for Central Server"
    Value: !Sub "https://${CentralServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CentralServerApiUrl"

  CentralServerSwaggerUrl:
    Description: "Swagger/OpenAPI documentation URL for Central Server"
    Value: !Sub "https://${CentralServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/swagger"
    Export:
      Name: !Sub "${AWS::StackName}-CentralServerSwaggerUrl"

  CompanyAServerApiUrl:
    Description: "API Gateway endpoint URL for Company A Server"
    Value: !Sub "https://${CompanyAServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyAServerApiUrl"

  CompanyAServerSwaggerUrl:
    Description: "Swagger/OpenAPI documentation URL for Company A Server"
    Value: !Sub "https://${CompanyAServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/swagger"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyAServerSwaggerUrl"

  CompanyBServerApiUrl:
    Description: "API Gateway endpoint URL for Company B Server"
    Value: !Sub "https://${CompanyBServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyBServerApiUrl"

  CompanyBServerSwaggerUrl:
    Description: "Swagger/OpenAPI documentation URL for Company B Server"
    Value: !Sub "https://${CompanyBServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/swagger"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyBServerSwaggerUrl"

  CompanyCServerApiUrl:
    Description: "API Gateway endpoint URL for Company C Server"
    Value: !Sub "https://${CompanyCServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyCServerApiUrl"

  CompanyCServerSwaggerUrl:
    Description: "Swagger/OpenAPI documentation URL for Company C Server"
    Value: !Sub "https://${CompanyCServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/swagger"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyCServerSwaggerUrl"

  # OpenAPI/Swagger Export URLs
  CentralServerOpenApiUrl:
    Description: "OpenAPI 3.0 specification URL for Central Server (JSON)"
    Value: !Sub "https://${CentralServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/v1/swagger.json"
    Export:
      Name: !Sub "${AWS::StackName}-CentralServerOpenApiUrl"

  CompanyAServerOpenApiUrl:
    Description: "OpenAPI 3.0 specification URL for Company A Server (JSON)"
    Value: !Sub "https://${CompanyAServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/v1/swagger.json"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyAServerOpenApiUrl"

  CompanyBServerOpenApiUrl:
    Description: "OpenAPI 3.0 specification URL for Company B Server (JSON)"
    Value: !Sub "https://${CompanyBServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/v1/swagger.json"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyBServerOpenApiUrl"

  CompanyCServerOpenApiUrl:
    Description: "OpenAPI 3.0 specification URL for Company C Server (JSON)"
    Value: !Sub "https://${CompanyCServerApi}.execute-api.${AWS::Region}.amazonaws.com/${Environment}/v1/swagger.json"
    Export:
      Name: !Sub "${AWS::StackName}-CompanyCServerOpenApiUrl"

  DatabaseHost:
    Description: "PostgreSQL Database Host (External)"
    Value: !Ref DatabaseHost
    Export:
      Name: !Sub "${AWS::StackName}-DatabaseHost"

  DatabasePort:
    Description: "PostgreSQL Database Port"
    Value: !Ref DatabasePort
    Export:
      Name: !Sub "${AWS::StackName}-DatabasePort"
