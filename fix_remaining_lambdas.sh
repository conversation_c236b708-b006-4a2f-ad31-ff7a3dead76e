#!/bin/bash

# Script to fix remaining Lambda functions with BuildMethod: makefile and proper Makefiles

# List of Lambda functions that need BuildMethod: makefile metadata added
# and their corresponding Makefile paths

# Functions that need fixing based on the error patterns
FUNCTIONS_TO_FIX=(
    "CompanyBAsyncQueryFunction:lambdas/company-query-async"
    "CompanyCAsyncQueryFunction:lambdas/company-query-async"
    "CompanyAPollFunction:lambdas/company-query-poll"
    "CompanyBPollFunction:lambdas/company-query-poll"
    "CompanyCPollFunction:lambdas/company-query-poll"
    "CompanyADecryptFunction:lambdas/company-decrypt"
    "CompanyBDecryptFunction:lambdas/company-decrypt"
    "CompanyCDecryptFunction:lambdas/company-decrypt"
    "CentralCrossQueryFunction:lambdas/central-cross-query"
    "CentralGetDataFunction:lambdas/central-get-data"
    "DatabaseMigrationFunction:lambdas/db-migration"
)

# Function to add BuildMethod: makefile to template.yaml
add_build_method() {
    local function_name=$1
    echo "Adding BuildMethod: makefile to $function_name"
    
    # This would need to be done manually for each function in template.yaml
    # as the structure varies slightly for each function
}

# Function to fix Makefile for a given lambda directory
fix_makefile() {
    local lambda_path=$1
    local makefile_path="$lambda_path/Makefile"
    
    echo "Fixing Makefile for $lambda_path"
    
    # Create the new Makefile content
    cat > "$makefile_path" << 'EOF'
.PHONY: build clean

# Go build parameters
GOOS=linux
GOARCH=amd64
CGO_ENABLED=0

# If ARTIFACTS_DIR is not set by SAM, use the current directory
ARTIFACTS_DIR?=.

build:
	@echo "Building Lambda function..."
	@echo "Current directory: $$(pwd)"
	@echo "Artifacts directory: $(ARTIFACTS_DIR)"
	cd /Users/<USER>/Downloads/secure_double_blind_ec && \
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
	go build -ldflags="-s -w" -o "$(ARTIFACTS_DIR)/bootstrap" LAMBDA_PATH_PLACEHOLDER/main.go
	@echo "Lambda function built successfully!"

clean:
	@echo "Cleaning build artifacts..."
	rm -f bootstrap
	@echo "Clean completed!"
EOF

    # Replace the placeholder with the actual lambda path
    sed -i '' "s|LAMBDA_PATH_PLACEHOLDER|$lambda_path|g" "$makefile_path"
    
    # Add specific build targets if they exist in the original Makefile
    if [ -f "$makefile_path.bak" ]; then
        # Extract build targets from the backup
        grep "^build-.*:" "$makefile_path.bak" >> "$makefile_path" 2>/dev/null || true
    fi
}

# Main execution
echo "Starting Lambda function fixes..."

# Fix Makefiles for unique lambda directories
UNIQUE_DIRS=(
    "lambdas/company-query-poll"
    "lambdas/company-decrypt"
    "lambdas/central-cross-query"
    "lambdas/central-get-data"
    "lambdas/db-migration"
)

for dir in "${UNIQUE_DIRS[@]}"; do
    if [ -f "$dir/Makefile" ]; then
        # Backup original Makefile
        cp "$dir/Makefile" "$dir/Makefile.bak"
        fix_makefile "$dir"
    fi
done

echo "Makefile fixes completed!"
echo "Note: You still need to manually add BuildMethod: makefile metadata to the template.yaml for each function."
