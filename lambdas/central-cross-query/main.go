package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
)

type CrossQueryRequest struct {
	BlindedIdentifier string `json:"blinded_identifier"` // Hex-encoded blinded identifier
	RequestingCompany string `json:"requesting_company"` // Company making the request
}

type CompanyEncryptRequest struct {
	EncryptedValue string `json:"encrypted_value"` // Hex-encoded encrypted value
	CompanyID      string `json:"company_id"`      // Target company's ID
}

type CrossQueryResponse struct {
	Success       bool              `json:"success"`
	Message       string            `json:"message"`
	BlindedValues map[string]string `json:"blinded_values,omitempty"` // Map of company_id -> double_encrypted_value (hex)
}

// List of all companies in the system (should be configurable)
var allCompanies = []string{"company_a", "company_b", "company_c"}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Parse request body
	var req CrossQueryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.BlindedIdentifier == "" || req.RequestingCompany == "" {
		return createErrorResponse(http.StatusBadRequest, "Missing required fields")
	}

	// Use the provided blinded identifier hex string directly
	// No need to recalculate since it's already provided as hex-encoded
	baseEncryptedHex := req.BlindedIdentifier

	// Map to store double-encrypted values from all companies
	blindedValues := make(map[string]string)

	// Call each company's encrypt API to get double-encrypted values
	for _, companyID := range allCompanies {
		if companyID == req.RequestingCompany {
			// For the requesting company, use the original blinded identifier
			blindedValues[companyID] = req.BlindedIdentifier
			continue
		}

		// Call the company's encrypt API
		encryptReq := CompanyEncryptRequest{
			EncryptedValue: baseEncryptedHex,
			CompanyID:      companyID,
		}

		doubleEncryptedValue, err := callCompanyEncryptAPI(companyID, encryptReq)
		if err != nil {
			// Log the error but continue with other companies
			fmt.Printf("Failed to get encryption from company %s: %v\n", companyID, err)
			continue
		}

		blindedValues[companyID] = doubleEncryptedValue
	}

	// Prepare response
	response := CrossQueryResponse{
		Success:       true,
		Message:       "Cross-company query completed successfully",
		BlindedValues: blindedValues,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func callCompanyEncryptAPI(companyID string, req CompanyEncryptRequest) (string, error) {
	companyServerURL := os.Getenv("COMPANY_SERVER_URL")
	if companyServerURL == "" {
		// Map company IDs to their API URLs
		companyURLs := map[string]string{
			"company_a": "https://bx7ztelqn4.execute-api.ap-south-1.amazonaws.com/dev",
			"company_b": "https://556sf0sbw5.execute-api.ap-south-1.amazonaws.com/dev",
			"company_c": "https://31e9j4fkc0.execute-api.ap-south-1.amazonaws.com/dev",
		}
		companyServerURL = companyURLs[companyID]
		if companyServerURL == "" {
			return "", fmt.Errorf("unknown company ID: %s", companyID)
		}
	}

	requestBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(companyServerURL+"/company-"+companyID[len(companyID)-1:]+"/encrypt", "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to call company encrypt API: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("company server returned status: %d", resp.StatusCode)
	}

	var response struct {
		Success              bool   `json:"success"`
		DoubleEncryptedValue string `json:"double_encrypted_value"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %v", err)
	}

	if !response.Success {
		return "", fmt.Errorf("company encrypt API returned failure")
	}

	return response.DoubleEncryptedValue, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
