package main

import (
	"bytes"
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/cloudflare/circl/group"
	"secure_double_blind_ec/internal/company"
)

type QueryRequest struct {
	Identifier string `json:"identifier"` // Plain text identifier to query
	CompanyID  string `json:"company_id"` // This company's ID
}

type CentralCrossQueryRequest struct {
	BlindedIdentifier string `json:"blinded_identifier"` // Hex-encoded blinded identifier
	RequestingCompany string `json:"requesting_company"` // Company making the request
}

type CentralGetDataRequest struct {
	BlindedValues map[string]string `json:"blinded_values"` // Map of company_id -> blinded_value (hex)
}

type QueryResponse struct {
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	FraudData   []FraudDataResult      `json:"fraud_data,omitempty"`
}

type FraudDataResult struct {
	ID             int                    `json:"id"`
	Identifier     string                 `json:"identifier"`
	IdentifierType string                 `json:"identifier_type"`
	Metadata       map[string]interface{} `json:"metadata"`
	InstitutionID  int                    `json:"institution_id"`
	CreatedBy      string                 `json:"created_by"`
	Status         string                 `json:"status"`
	FraudType      string                 `json:"fraud_type"`
	CreatedAt      string                 `json:"created_at"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-query")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req QueryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.Identifier == "" || req.CompanyID == "" {
		log.Printf("Missing required fields: identifier or company_id")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: identifier, company_id")
	}
	log.Printf("Initializing blinding service for company: %s", req.CompanyID)
	// Initialize blinding service for the company
	blindingService, err := company.NewBlindingService(req.CompanyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to initialize blinding service: %v", err))
	}
	log.Println("Blinding service initialized successfully")
	// Blind the identifier for query
	blindedPoint, err := blindingService.BlindDataForQuery(req.Identifier)
	if err != nil {
		log.Printf("Failed to blind identifier: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to blind identifier: %v", err))
	}

	// Serialize the blinded point
	blindedBytes, err := blindedPoint.MarshalBinary()
	if err != nil {
		log.Printf("Failed to serialize blinded identifier: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize blinded identifier")
	}
	blindedHex := hex.EncodeToString(blindedBytes)
	log.Printf("Blinded identifier (hex): %s", blindedHex)

	// Step 1: Call central server to get cross-company blinded values
	crossQueryReq := CentralCrossQueryRequest{
		BlindedIdentifier: blindedHex,
		RequestingCompany: req.CompanyID,
	}
	log.Printf("Calling central server for cross-company blinded values: %+v", crossQueryReq)
	// Step 1: Call central server to get cross-company blinded values
	crossQueryResponse, err := callCentralCrossQuery(crossQueryReq)
	if err != nil {
		log.Printf("Failed to get cross-company data: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to get cross-company data: %v", err))
	}
	log.Printf("Received cross-company response: %+v", crossQueryResponse)
	// Convert the response to the expected format for decryption
	groupElements, err := convertResponseToGroupElements(crossQueryResponse)
	if err != nil {
		log.Printf("Failed to convert response format: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to convert response format: %v", err))
	}
	log.Println("Converted response to group elements for decryption")
	// Step 2: Decrypt the double-encrypted values to get other companies' blinded values
	decryptedValues, err := blindingService.DecryptDoubleEncryptedValues(groupElements)
	if err != nil {
		log.Printf("Failed to decrypt double-encrypted values: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to decrypt double-encrypted values: %v", err))
	}
	log.Printf("Decrypted values: %+v", decryptedValues)
	// Step 3: Call central server to get actual fraud data using all blinded values
	getDataReq := CentralGetDataRequest{
		BlindedValues: decryptedValues,
	}
	log.Printf("Calling central server to get fraud data: %+v", getDataReq)
	fraudData, err := callCentralGetData(getDataReq)
	if err != nil {
		log.Printf("Failed to get fraud data: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to get fraud data: %v", err))
	}
	log.Printf("Received fraud data: %d records", len(fraudData))
	// Prepare response
	response := QueryResponse{
		Success:   true,
		Message:   fmt.Sprintf("Found %d fraud data records", len(fraudData)),
		FraudData: fraudData,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func callCentralCrossQuery(req CentralCrossQueryRequest) (map[string]interface{}, error) {
	centralServerURL := os.Getenv("CENTRAL_SERVER_URL")
	if centralServerURL == "" {
		centralServerURL = "https://cd8bofmqb0.execute-api.ap-south-1.amazonaws.com/dev"
	}

	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(centralServerURL+"/central/cross-query", "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to call central cross-query: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("central server returned status: %d", resp.StatusCode)
	}

	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	// Extract the blinded_values from the response
	if blindedValues, ok := response["blinded_values"].(map[string]interface{}); ok {
		// Convert interface{} values to strings
		result := make(map[string]interface{})
		for k, v := range blindedValues {
			if strVal, ok := v.(string); ok {
				result[k] = strVal
			}
		}
		return result, nil
	}

	return nil, fmt.Errorf("invalid response format from central server")
}

func callCentralGetData(req CentralGetDataRequest) ([]FraudDataResult, error) {
	centralServerURL := os.Getenv("CENTRAL_SERVER_URL")
	if centralServerURL == "" {
		centralServerURL = "https://cd8bofmqb0.execute-api.ap-south-1.amazonaws.com/dev"
	}

	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(centralServerURL+"/central/get-data", "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to call central get-data: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("central server returned status: %d", resp.StatusCode)
	}

	var response struct {
		Success   bool              `json:"success"`
		FraudData []FraudDataResult `json:"fraud_data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	return response.FraudData, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

// convertResponseToGroupElements converts the API response to group.Element map
func convertResponseToGroupElements(response map[string]interface{}) (map[string]group.Element, error) {
	result := make(map[string]group.Element)
	g := group.P256 // Using the same group as the blinding service

	for companyID, value := range response {
		// Convert the value to string (it should be hex-encoded)
		strValue, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("expected string value for company %s, got %T", companyID, value)
		}

		// Decode hex string to bytes
		bytes, err := hex.DecodeString(strValue)
		if err != nil {
			return nil, fmt.Errorf("failed to decode hex for company %s: %v", companyID, err)
		}

		// Unmarshal bytes to group element
		element := g.NewElement()
		if err := element.UnmarshalBinary(bytes); err != nil {
			return nil, fmt.Errorf("failed to unmarshal group element for company %s: %v", companyID, err)
		}

		result[companyID] = element
	}

	return result, nil
}

func main() {
	lambda.Start(handler)
}
