package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/internal/company"
	"log"
)

type Identifier struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type SubmitRequest struct {
	Identifiers       []Identifier           `json:"identifiers"`
	Metadata          map[string]interface{} `json:"metadata"`
	InstitutionID     int                    `json:"institution_id"`
	Status            string                 `json:"status"`
	FraudType         string                 `json:"fraud_type"`
	CreatedBy         string                 `json:"created_by"`
	CompanyID         string                 `json:"company_id"`
}

type CentralStoreRequest struct {
	BlindedID       string                 `json:"blinded_id"`
	IdentifierType  string                 `json:"identifier_type"`
	Metadata        map[string]interface{} `json:"metadata"`
	InstitutionID   int                    `json:"institution_id"`
	CreatedBy       string                 `json:"created_by"`
	Status          string                 `json:"status"`
	FraudType       string                 `json:"fraud_type"`
	Identifiers     []IdentifierData       `json:"identifiers,omitempty"`
}

type IdentifierData struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type SubmitResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	RequestID string `json:"request_id,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-submit")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req SubmitRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)

	// Validate required fields
	if len(req.Identifiers) == 0 || req.InstitutionID == 0 || 
	   req.CreatedBy == "" || req.Status == "" || req.FraudType == "" || req.CompanyID == "" {
		log.Printf("Missing required fields in request")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields")
	}

	// Validate enum values
	if !isValidStatus(req.Status) {
		log.Printf("Invalid status: %s", req.Status)
		return createErrorResponse(http.StatusBadRequest, "Invalid status. Must be one of: Confirmed, Suspected, Absolved")
	}

	if !isValidFraudType(req.FraudType) {
		log.Printf("Invalid fraud_type: %s", req.FraudType)
		return createErrorResponse(http.StatusBadRequest, "Invalid fraud_type. Must be one of: Mule, Identity Theft, Fraud")
	}
	log.Printf("Initializing blinding service for company: %s", req.CompanyID)
	// Initialize blinding service for the company
	blindingService, err := company.NewBlindingService(req.CompanyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to initialize blinding service: %v", err))
	}
	log.Println("Blinding service initialized successfully")

	// Process each identifier
	var blindedIdentifiers []IdentifierData
	for _, identifier := range req.Identifiers {
		log.Printf("Blinding identifier: %+v", identifier)
		// Blind the identifier
		blindedPoint, err := blindingService.BlindData(identifier.ID)
		if err != nil {
			log.Printf("Failed to blind identifier: %v", err)
			return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to blind identifier: %v", err))
		}

		// Serialize the blinded point
		blindedBytes, err := blindedPoint.MarshalBinary()
		if err != nil {
			log.Printf("Failed to serialize blinded data: %v", err)
			return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to serialize blinded data: %v", err))
		}

		blindedIdentifiers = append(blindedIdentifiers, IdentifierData{
			ID:   fmt.Sprintf("%x", blindedBytes), // Convert to hex string
			Type: identifier.Type,
		})
	}

	// Use the first identifier as the primary blinded ID
	primaryBlindedID := blindedIdentifiers[0].ID
	primaryType := blindedIdentifiers[0].Type

	// Prepare request to central server
	centralReq := CentralStoreRequest{
		BlindedID:      primaryBlindedID,
		IdentifierType: primaryType,
		Metadata:       req.Metadata,
		InstitutionID:  req.InstitutionID,
		CreatedBy:      req.CreatedBy,
		Status:         req.Status,
		FraudType:      req.FraudType,
	}

	// Include all identifiers if there are multiple
	if len(blindedIdentifiers) > 1 {
		centralReq.Identifiers = blindedIdentifiers
	}
	log.Printf("Calling central server API with request: %+v", centralReq)
	// Call central server API
	err = callCentralStoreAPI(centralReq)
	if err != nil {
		log.Printf("Failed to submit to central server: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to submit to central server: %v", err))
	}
	log.Println("Successfully submitted fraud data to central server")

	// Prepare response
	response := SubmitResponse{
		Success: true,
		Message: "Fraud data submitted successfully",
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func callCentralStoreAPI(req CentralStoreRequest) error {
	centralServerURL := os.Getenv("CENTRAL_SERVER_URL")
	if centralServerURL == "" {
		centralServerURL = "https://cd8bofmqb0.execute-api.ap-south-1.amazonaws.com/dev"
	}

	requestBody, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(centralServerURL+"/central/store", "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return fmt.Errorf("failed to call central server: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("central server returned status: %d", resp.StatusCode)
	}

	return nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"Confirmed": true,
		"Suspected": true,
		"Absolved":  true,
	}
	return validStatuses[status]
}

func isValidFraudType(fraudType string) bool {
	validTypes := map[string]bool{
		"Mule":           true,
		"Identity Theft": true,
		"Fraud":          true,
	}
	return validTypes[fraudType]
}

func main() {
	lambda.Start(handler)
}
