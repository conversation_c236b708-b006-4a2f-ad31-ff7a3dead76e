package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
	"log"
)

type StoreRequest struct {
	BlindedID        string                 `json:"blinded_id"`
	IdentifierType   string                 `json:"identifier_type"`
	Metadata         map[string]interface{} `json:"metadata"`
	InstitutionID    int                    `json:"institution_id"`
	CreatedBy        string                 `json:"created_by"`
	Status           string                 `json:"status"`
	FraudType        string                 `json:"fraud_type"`
	Identifiers      []IdentifierData       `json:"identifiers,omitempty"`
}

type IdentifierData struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type StoreResponse struct {
	Success       bool   `json:"success"`
	Message       string `json:"message"`
	FraudDataID   int    `json:"fraud_data_id,omitempty"`
	AssociationID *int   `json:"association_id,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: central-store")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req StoreRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)

	// Validate required fields
	if req.BlindedID == "" || req.IdentifierType == "" || req.InstitutionID == 0 || 
	   req.CreatedBy == "" || req.Status == "" || req.FraudType == "" {
		log.Printf("Missing required fields in request")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields")
	}

	// Validate enum values
	if !isValidStatus(req.Status) {
		log.Printf("Invalid status: %s", req.Status)
		return createErrorResponse(http.StatusBadRequest, "Invalid status. Must be one of: Confirmed, Suspected, Absolved")
	}

	if !isValidFraudType(req.FraudType) {
		log.Printf("Invalid fraud_type: %s", req.FraudType)
		return createErrorResponse(http.StatusBadRequest, "Invalid fraud_type. Must be one of: Mule, Identity Theft, Fraud")
	}
	log.Println("Connecting to database...")
	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	var associationID *int

	// Create association if multiple identifiers provided
	if len(req.Identifiers) > 1 {
		log.Printf("Creating ID association for multiple identifiers: %+v", req.Identifiers)
		// Create associations map
		associations := map[string]interface{}{
			"identifiers": req.Identifiers,
			"primary_id":  req.BlindedID,
		}

		idAssoc := &database.IDAssociation{
			Associations: associations,
		}

		if err := db.CreateIDAssociation(idAssoc); err != nil {
			log.Printf("Failed to create ID association: %v", err)
			return createErrorResponse(http.StatusInternalServerError, "Failed to create ID association")
		}

		associationID = &idAssoc.ID
	}
	log.Println("Creating fraud data record...")
	// Create fraud data record
	fraudData := &database.FraudData{
		Identifier:     req.BlindedID,
		IdentifierType: req.IdentifierType,
		Metadata:       req.Metadata,
		InstitutionID:  req.InstitutionID,
		CreatedBy:      req.CreatedBy,
		Status:         req.Status,
		FraudType:      req.FraudType,
		AssociationID:  associationID,
	}

	if err := db.CreateFraudData(fraudData); err != nil {
		log.Printf("Failed to store fraud data: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to store fraud data")
	}
	log.Printf("Fraud data stored with ID: %d", fraudData.ID)

	// Create audit log
	auditLog := &database.AuditLog{
		EventType:     "FRAUD_DATA_CREATED",
		InstitutionID: &req.InstitutionID,
		UserID:        req.CreatedBy,
		Metadata: map[string]interface{}{
			"fraud_data_id":   fraudData.ID,
			"identifier_type": req.IdentifierType,
			"status":          req.Status,
			"fraud_type":      req.FraudType,
			"association_id":  associationID,
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Log the error but don't fail the request
		fmt.Printf("Failed to create audit log: %v\n", err)
	}

	// Prepare response
	response := StoreResponse{
		Success:       true,
		Message:       "Fraud data stored successfully",
		FraudDataID:   fraudData.ID,
		AssociationID: associationID,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusCreated,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"Confirmed": true,
		"Suspected": true,
		"Absolved":  true,
	}
	return validStatuses[status]
}

func isValidFraudType(fraudType string) bool {
	validTypes := map[string]bool{
		"Mule":           true,
		"Identity Theft": true,
		"Fraud":          true,
	}
	return validTypes[fraudType]
}

func main() {
	lambda.Start(handler)
}
