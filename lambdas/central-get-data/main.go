package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
)

type GetDataRequest struct {
	BlindedValues map[string]string `json:"blinded_values"` // Map of company_id -> blinded_value (hex)
}

type GetDataResponse struct {
	Success   bool              `json:"success"`
	Message   string            `json:"message"`
	FraudData []FraudDataResult `json:"fraud_data,omitempty"`
}

type FraudDataResult struct {
	ID             int                    `json:"id"`
	Identifier     string                 `json:"identifier"`
	IdentifierType string                 `json:"identifier_type"`
	Metadata       map[string]interface{} `json:"metadata"`
	InstitutionID  int                    `json:"institution_id"`
	CreatedBy      string                 `json:"created_by"`
	Status         string                 `json:"status"`
	FraudType      string                 `json:"fraud_type"`
	CreatedAt      string                 `json:"created_at"`
	UpdatedAt      string                 `json:"updated_at"`
	AssociationID  *int                   `json:"association_id"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Parse request body
	var req GetDataRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if len(req.BlindedValues) == 0 {
		return createErrorResponse(http.StatusBadRequest, "Missing required field: blinded_values")
	}

	// Connect to database
	db, err := database.New()
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	var allFraudData []FraudDataResult

	// Search for fraud data using each blinded value
	for companyID, blindedValue := range req.BlindedValues {
		fraudDataList, err := db.GetFraudDataByIdentifier(blindedValue)
		if err != nil {
			// Log the error but continue with other identifiers
			fmt.Printf("Failed to query fraud data for company %s: %v\n", companyID, err)
			continue
		}

		// Convert database results to response format
		for _, fraud := range fraudDataList {
			result := FraudDataResult{
				ID:             fraud.ID,
				Identifier:     fraud.Identifier,
				IdentifierType: fraud.IdentifierType,
				Metadata:       fraud.Metadata,
				InstitutionID:  fraud.InstitutionID,
				CreatedBy:      fraud.CreatedBy,
				Status:         fraud.Status,
				FraudType:      fraud.FraudType,
				CreatedAt:      fraud.CreatedAt.Format(time.RFC3339),
				UpdatedAt:      fraud.UpdatedAt.Format(time.RFC3339),
				AssociationID:  fraud.AssociationID,
			}
			allFraudData = append(allFraudData, result)
		}
	}

	// Create audit log for the query
	auditLog := &database.AuditLog{
		EventType: "FRAUD_DATA_QUERIED",
		UserID:    "system", // Since this is called by the central server
		Metadata: map[string]interface{}{
			"query_type":        "cross_company_search",
			"companies_queried": getCompanyIDs(req.BlindedValues),
			"results_count":     len(allFraudData),
			// Removed original_data_hash since we no longer have original data
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		// Log the error but don't fail the request
		fmt.Printf("Failed to create audit log: %v\n", err)
	}

	// Prepare response
	response := GetDataResponse{
		Success:   true,
		Message:   fmt.Sprintf("Found %d fraud data records", len(allFraudData)),
		FraudData: allFraudData,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func getCompanyIDs(blindedValues map[string]string) []string {
	var companies []string
	for companyID := range blindedValues {
		companies = append(companies, companyID)
	}
	return companies
}

func hashString(data string) string {
	// Simple hash for audit purposes (in production, use proper crypto hash)
	hash := 0
	for _, char := range data {
		hash = hash*31 + int(char)
	}
	return fmt.Sprintf("%x", hash)
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
