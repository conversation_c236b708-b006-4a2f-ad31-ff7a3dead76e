package main

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
)

type CreateInstitutionRequest struct {
	InstitutionName string `json:"institution_name"`
	AccessRole      string `json:"access_role"`
	CreatedBy       string `json:"created_by"`
	Status          string `json:"status,omitempty"`
}

type CreateInstitutionResponse struct {
	Success     bool                    `json:"success"`
	Message     string                  `json:"message"`
	Institution *database.Institution   `json:"institution,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Parse request body
	var req CreateInstitutionRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.InstitutionName == "" || req.AccessRole == "" || req.CreatedBy == "" {
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: institution_name, access_role, created_by")
	}

	// Set default status if not provided
	if req.Status == "" {
		req.Status = "active"
	}

	// Validate status
	if !isValidStatus(req.Status) {
		return createErrorResponse(http.StatusBadRequest, "Invalid status. Must be 'active' or 'inactive'")
	}

	// Validate access role
	if !isValidAccessRole(req.AccessRole) {
		return createErrorResponse(http.StatusBadRequest, "Invalid access_role. Must be 'admin' or 'company'")
	}

	// Connect to database
	db, err := database.New()
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	// Check if institution already exists
	existingInst, err := db.GetInstitutionByName(req.InstitutionName)
	if err == nil && existingInst != nil {
		return createErrorResponse(http.StatusConflict, "Institution with this name already exists")
	}

	// Create institution
	institution := &database.Institution{
		InstitutionName: req.InstitutionName,
		AccessRole:      req.AccessRole,
		CreatedBy:       req.CreatedBy,
		Status:          req.Status,
	}

	if err := db.CreateInstitution(institution); err != nil {
		return createErrorResponse(http.StatusInternalServerError, "Failed to create institution")
	}

	// Create audit log
	auditLog := &database.AuditLog{
		EventType:     "INSTITUTION_CREATED",
		InstitutionID: &institution.ID,
		UserID:        req.CreatedBy,
		Metadata: map[string]interface{}{
			"institution_name": req.InstitutionName,
			"access_role":      req.AccessRole,
			"status":           req.Status,
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		// Log the error but don't fail the request
		// fmt.Printf("Failed to create audit log: %v\n", err)
	}

	// Prepare response
	response := CreateInstitutionResponse{
		Success:     true,
		Message:     "Institution created successfully",
		Institution: institution,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusCreated,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"active":   true,
		"inactive": true,
	}
	return validStatuses[status]
}

func isValidAccessRole(role string) bool {
	validRoles := map[string]bool{
		"admin":   true,
		"company": true,
	}
	return validRoles[role]
}

func main() {
	lambda.Start(handler)
}
