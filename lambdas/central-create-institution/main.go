package main

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"secure_double_blind_ec/pkg/database"
	"log"
)

type CreateInstitutionRequest struct {
	InstitutionName string `json:"institution_name"`
	AccessRole      string `json:"access_role"`
	CreatedBy       string `json:"created_by"`
	Status          string `json:"status,omitempty"`
}

type CreateInstitutionResponse struct {
	Success     bool                    `json:"success"`
	Message     string                  `json:"message"`
	Institution *database.Institution   `json:"institution,omitempty"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: central-create-institution")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req CreateInstitutionRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.InstitutionName == "" || req.AccessRole == "" || req.CreatedBy == "" {
		log.Printf("Missing required fields: institution_name, access_role, created_by")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: institution_name, access_role, created_by")
	}

	// Set default status if not provided
	if req.Status == "" {
		log.Println("No status provided, defaulting to 'active'")
		req.Status = "active"
	}

	// Validate status
	if !isValidStatus(req.Status) {
		log.Printf("Invalid status: %s", req.Status)
		return createErrorResponse(http.StatusBadRequest, "Invalid status. Must be 'active' or 'inactive'")
	}

	// Validate access role
	if !isValidAccessRole(req.AccessRole) {
		log.Printf("Invalid access_role: %s", req.AccessRole)
		return createErrorResponse(http.StatusBadRequest, "Invalid access_role. Must be 'admin' or 'company'")
	}

	// Connect to database
	log.Println("Connecting to database...")
	db, err := database.New()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()
	log.Printf("Checking if institution already exists: %s", req.InstitutionName)
	// Check if institution already exists
	existingInst, err := db.GetInstitutionByName(req.InstitutionName)
	if err == nil && existingInst != nil {
		log.Printf("Institution already exists: %s", req.InstitutionName)
		return createErrorResponse(http.StatusConflict, "Institution with this name already exists")
	}

	// Create institution
	log.Println("Creating institution record...")
	institution := &database.Institution{
		InstitutionName: req.InstitutionName,
		AccessRole:      req.AccessRole,
		CreatedBy:       req.CreatedBy,
		Status:          req.Status,
	}

	if err := db.CreateInstitution(institution); err != nil {
		log.Printf("Failed to create institution: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to create institution")
	}
	log.Printf("Institution created with ID: %d", institution.ID)

	// Create audit log
	auditLog := &database.AuditLog{
		EventType:     "INSTITUTION_CREATED",
		InstitutionID: &institution.ID,
		UserID:        req.CreatedBy,
		Metadata: map[string]interface{}{
			"institution_name": req.InstitutionName,
			"access_role":      req.AccessRole,
			"status":           req.Status,
		},
	}

	if err := db.CreateAuditLog(auditLog); err != nil {
		log.Printf("Failed to create audit log: %v", err)
		// Log the error but don't fail the request
		// fmt.Printf("Failed to create audit log: %v\n", err)
	}

	// Prepare response
	response := CreateInstitutionResponse{
		Success:     true,
		Message:     "Institution created successfully",
		Institution: institution,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusCreated,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func isValidStatus(status string) bool {
	validStatuses := map[string]bool{
		"active":   true,
		"inactive": true,
	}
	return validStatuses[status]
}

func isValidAccessRole(role string) bool {
	validRoles := map[string]bool{
		"admin":   true,
		"company": true,
	}
	return validRoles[role]
}

func main() {
	lambda.Start(handler)
}
