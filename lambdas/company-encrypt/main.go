package main

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/cloudflare/circl/group"
	"secure_double_blind_ec/internal/company"
)

type EncryptRequest struct {
	EncryptedValue string `json:"encrypted_value"` // Hex-encoded encrypted value from another company
	OriginalData   string `json:"original_data"`   // Original data for initial encryption (optional)
	CompanyID      string `json:"company_id"`      // This company's ID
}

type EncryptResponse struct {
	Success              bool   `json:"success"`
	Message              string `json:"message"`
	DoubleEncryptedValue string `json:"double_encrypted_value,omitempty"` // Hex-encoded
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Parse request body
	var req EncryptRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.CompanyID == "" {
		return createErrorResponse(http.StatusBadRequest, "Missing required field: company_id")
	}

	// Either encrypted_value OR original_data must be provided, but not both
	if req.EncryptedValue != "" && req.OriginalData != "" {
		return createErrorResponse(http.StatusBadRequest, "Provide either encrypted_value OR original_data, not both")
	}
	
	if req.EncryptedValue == "" && req.OriginalData == "" {
		return createErrorResponse(http.StatusBadRequest, "Either encrypted_value or original_data must be provided")
	}

	// Initialize blinding service for the company
	blindingService, err := company.NewBlindingService(req.CompanyID)
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to initialize blinding service: %v", err))
	}

	var doubleEncryptedPoint group.Element
	g := group.P256 // Using the same group as the blinding service

	// Check if this is initial encryption (original data provided) or double encryption
	if req.OriginalData != "" {
		// Initial encryption: Company encrypts its own data
		encryptedPoint, err := blindingService.BlindData(req.OriginalData)
		if err != nil {
			return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to encrypt original data: %v", err))
		}
		doubleEncryptedPoint = encryptedPoint
	} else {
		// Double encryption: Company encrypts already encrypted value from another company
		
		// Decode the encrypted value from hex
		encryptedBytes, err := hex.DecodeString(req.EncryptedValue)
		if err != nil {
			return createErrorResponse(http.StatusBadRequest, "Invalid hex-encoded encrypted_value")
		}

		// Unmarshal the encrypted value back to a group element
		encryptedPoint := g.NewElement()
		if err := encryptedPoint.UnmarshalBinary(encryptedBytes); err != nil {
			return createErrorResponse(http.StatusBadRequest, "Failed to unmarshal encrypted value")
		}

		// Apply this company's encryption to the already encrypted value
		doubleEncryptedPoint, err = blindingService.EncryptAlreadyEncryptedValue(encryptedPoint)
		if err != nil {
			return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to apply double encryption: %v", err))
		}
	}

	// Serialize the encrypted result
	doubleEncryptedBytes, err := doubleEncryptedPoint.MarshalBinary()
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize encrypted value")
	}

	// Convert to hex string for response
	doubleEncryptedHex := hex.EncodeToString(doubleEncryptedBytes)

	// Prepare response
	response := EncryptResponse{
		Success:              true,
		Message:              "Encryption applied successfully",
		DoubleEncryptedValue: doubleEncryptedHex,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
