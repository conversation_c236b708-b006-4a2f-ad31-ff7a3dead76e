package main

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/cloudflare/circl/group"
	"secure_double_blind_ec/internal/company"
)

type DecryptRequest struct {
	DoubleEncryptedValues map[string]string `json:"double_encrypted_values"` // Map of company_id -> encrypted_value (hex)
	CompanyID             string            `json:"company_id"`              // This company's ID
}

type DecryptResponse struct {
	Success         bool              `json:"success"`
	Message         string            `json:"message"`
	DecryptedValues map[string]string `json:"decrypted_values,omitempty"` // Map of company_id -> decrypted_value (hex)
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-decrypt")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req DecryptRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if len(req.DoubleEncryptedValues) == 0 || req.CompanyID == "" {
		log.Printf("Missing required fields: double_encrypted_values or company_id")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: double_encrypted_values, company_id")
	}
	log.Printf("Initializing blinding service for company: %s", req.CompanyID)
	// Initialize blinding service for the company
	blindingService, err := company.NewBlindingService(req.CompanyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to initialize blinding service: %v", err))
	}
	log.Println("Blinding service initialized successfully")
	g := group.P256 // Using the same group as the blinding service
	groupElements := make(map[string]group.Element)

	// Convert hex strings to group elements
	for companyID, hexValue := range req.DoubleEncryptedValues {
		log.Printf("Decoding and unmarshalling value for company: %s", companyID)
		// Decode hex to bytes
		encryptedBytes, err := hex.DecodeString(hexValue)
		if err != nil {
			log.Printf("Invalid hex value for %s: %v", companyID, err)
			return createErrorResponse(http.StatusBadRequest, fmt.Sprintf("Invalid hex value for %s", companyID))
		}

		// Unmarshal to group element
		element := g.NewElement()
		if err := element.UnmarshalBinary(encryptedBytes); err != nil {
			log.Printf("Failed to unmarshal encrypted value for %s: %v", companyID, err)
			return createErrorResponse(http.StatusBadRequest, fmt.Sprintf("Failed to unmarshal encrypted value for %s", companyID))
		}

		groupElements[companyID] = element
	}
	log.Println("Decrypting double-encrypted values...")
	// Decrypt the double-encrypted values
	decryptedValues, err := blindingService.DecryptDoubleEncryptedValues(groupElements)
	if err != nil {
		log.Printf("Failed to decrypt values: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to decrypt values: %v", err))
	}
	log.Printf("Decryption completed. Decrypted values: %+v", decryptedValues)
	// Prepare response
	response := DecryptResponse{
		Success:         true,
		Message:         "Decryption completed successfully",
		DecryptedValues: decryptedValues,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
