package main

import (
	"context"
	"database/sql"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/cfn"
	"github.com/aws/aws-lambda-go/lambda"
	_ "github.com/lib/pq"
)

func handler(ctx context.Context, event cfn.Event) (string, map[string]interface{}, error) {
	switch event.RequestType {
	case cfn.RequestCreate:
		return handleCreate(ctx, event)
	case cfn.RequestUpdate:
		return handleUpdate(ctx, event)
	case cfn.RequestDelete:
		return handleDelete(ctx, event)
	default:
		return "", nil, fmt.Errorf("unknown request type: %s", event.RequestType)
	}
}

func handleCreate(ctx context.Context, event cfn.Event) (string, map[string]interface{}, error) {
	err := runMigrations()
	if err != nil {
		return "migration-failed", nil, fmt.Errorf("failed to run migrations: %v", err)
	}

	// Return a valid physical resource ID
	physicalResourceID := fmt.Sprintf("migration-complete-%d", time.Now().Unix())
	return physicalResourceID, map[string]interface{}{
		"Status": "SUCCESS",
	}, nil
}

func handleUpdate(ctx context.Context, event cfn.Event) (string, map[string]interface{}, error) {
	// For updates, we can re-run migrations (they should be idempotent)
	err := runMigrations()
	if err != nil {
		return event.PhysicalResourceID, nil, fmt.Errorf("failed to run migrations: %v", err)
	}

	return event.PhysicalResourceID, map[string]interface{}{
		"Status": "SUCCESS",
	}, nil
}

func handleDelete(ctx context.Context, event cfn.Event) (string, map[string]interface{}, error) {
	// For delete, we don't need to do anything as the RDS cluster will be deleted
	return event.PhysicalResourceID, map[string]interface{}{
		"Status": "SUCCESS",
	}, nil
}

func runMigrations() error {
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USERNAME")
	password := os.Getenv("DB_PASSWORD")
	dbname := os.Getenv("DB_NAME")
	schema := os.Getenv("DB_SCHEMA")

	if host == "" {
		return fmt.Errorf("DB_HOST environment variable is required")
	}
	if port == "" {
		port = "5432"
	}
	if schema == "" {
		schema = "consortium"
	}

	// For external databases, use sslmode=require instead of prefer
	psqlInfo := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=require",
		host, port, user, password, dbname)

	db, err := sql.Open("postgres", psqlInfo)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}
	defer db.Close()

	if err = db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	// Create the consortium schema if it doesn't exist
	_, err = db.Exec(fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schema))
	if err != nil {
		return fmt.Errorf("failed to create schema: %v", err)
	}

	// Set search_path to use the consortium schema
	_, err = db.Exec(fmt.Sprintf("SET search_path TO %s", schema))
	if err != nil {
		return fmt.Errorf("failed to set search_path: %v", err)
	}

	// Read the schema SQL file
	schemaSQL, err := ioutil.ReadFile("schema.sql")
	if err != nil {
		// If the file doesn't exist in the current directory, use embedded schema
		schemaSQL = []byte(getEmbeddedSchema())
	}

	// Execute the schema
	_, err = db.Exec(string(schemaSQL))
	if err != nil {
		return fmt.Errorf("failed to execute schema: %v", err)
	}

	fmt.Printf("Database migrations completed successfully in schema: %s\n", schema)
	return nil
}

func getEmbeddedSchema() string {
	return `
-- Database schema for the secure double blind EC system

-- Create consortium schema if not exists
CREATE SCHEMA IF NOT EXISTS consortium;

-- Set search path to consortium schema
SET search_path TO consortium;

-- Institution table
CREATE TABLE IF NOT EXISTS institution (
    id SERIAL PRIMARY KEY,
    institution_name VARCHAR(255) NOT NULL UNIQUE,
    access_role VARCHAR(100) NOT NULL,
    created_by VARCHAR(255) NOT NULL,
    updated_by VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on institution_name for faster lookups
CREATE INDEX IF NOT EXISTS idx_institution_name ON institution(institution_name);
CREATE INDEX IF NOT EXISTS idx_institution_status ON institution(status);

-- ID Associations table
CREATE TABLE IF NOT EXISTS id_associations (
    id SERIAL PRIMARY KEY,
    associations JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on associations JSONB column
CREATE INDEX IF NOT EXISTS idx_id_associations_gin ON id_associations USING GIN(associations);

-- Fraud data table
CREATE TABLE IF NOT EXISTS fraud_data (
    id SERIAL PRIMARY KEY,
    identifier TEXT NOT NULL,
    identifier_type VARCHAR(50) NOT NULL,
    metadata JSONB,
    institution_id INTEGER NOT NULL REFERENCES institution(id) ON DELETE CASCADE,
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL CHECK (status IN ('Confirmed', 'Suspected', 'Absolved')),
    fraud_type VARCHAR(50) NOT NULL CHECK (fraud_type IN ('Mule', 'Identity Theft', 'Fraud')),
    updated_by VARCHAR(255),
    association_id INTEGER REFERENCES id_associations(id) ON DELETE SET NULL
);

-- Create indexes for fraud_data table
CREATE INDEX IF NOT EXISTS idx_fraud_data_identifier ON fraud_data(identifier);
CREATE INDEX IF NOT EXISTS idx_fraud_data_institution_id ON fraud_data(institution_id);
CREATE INDEX IF NOT EXISTS idx_fraud_data_status ON fraud_data(status);
CREATE INDEX IF NOT EXISTS idx_fraud_data_fraud_type ON fraud_data(fraud_type);
CREATE INDEX IF NOT EXISTS idx_fraud_data_identifier_type ON fraud_data(identifier_type);
CREATE INDEX IF NOT EXISTS idx_fraud_data_created_at ON fraud_data(created_at);
CREATE INDEX IF NOT EXISTS idx_fraud_data_association_id ON fraud_data(association_id);

-- Create GIN index on metadata JSONB column
CREATE INDEX IF NOT EXISTS idx_fraud_data_metadata_gin ON fraud_data USING GIN(metadata);

-- Audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    institution_id INTEGER REFERENCES institution(id) ON DELETE SET NULL,
    user_id VARCHAR(255) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for audit_logs table
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_institution_id ON audit_logs(institution_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- Create GIN index on metadata JSONB column
CREATE INDEX IF NOT EXISTS idx_audit_logs_metadata_gin ON audit_logs USING GIN(metadata);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic updated_at updates
DROP TRIGGER IF EXISTS update_institution_updated_at ON institution;
CREATE TRIGGER update_institution_updated_at 
    BEFORE UPDATE ON institution 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_fraud_data_updated_at ON fraud_data;
CREATE TRIGGER update_fraud_data_updated_at 
    BEFORE UPDATE ON fraud_data 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_id_associations_updated_at ON id_associations;
CREATE TRIGGER update_id_associations_updated_at 
    BEFORE UPDATE ON id_associations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_audit_logs_updated_at ON audit_logs;
CREATE TRIGGER update_audit_logs_updated_at 
    BEFORE UPDATE ON audit_logs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default institutions for testing
INSERT INTO institution (institution_name, access_role, created_by, status) 
VALUES 
    ('Central Authority', 'admin', 'system', 'active'),
    ('Company A', 'company', 'system', 'active'),
    ('Company B', 'company', 'system', 'active'),
    ('Company C', 'company', 'system', 'active')
ON CONFLICT (institution_name) DO NOTHING;
`
}

func main() {
	lambda.Start(cfn.LambdaWrap(handler))
}
