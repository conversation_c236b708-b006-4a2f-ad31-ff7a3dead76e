# Include the universal Makefile
include ./universal.mk

# Function-specific variables
FUNCTION_NAME=CompanyAAsyncQueryFunction
LAMBDA_PATH=lambdas/company-query-async

# Targets for SAM build
.PHONY: build-CompanyAAsyncQueryFunction build-CompanyBAsyncQueryFunction build-CompanyCAsyncQueryFunction

build-CompanyAAsyncQueryFunction: build
build-CompanyBAsyncQueryFunction: build
build-CompanyCAsyncQueryFunction: build
