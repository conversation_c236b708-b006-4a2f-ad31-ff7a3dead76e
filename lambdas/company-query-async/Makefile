.PHONY: build clean

# Go build parameters
GOOS=linux
GOARCH=amd64
CGO_ENABLED=0

# If ARTIFACTS_DIR is not set by SAM, use the current directory
ARTIFACTS_DIR?=.

build:
	@echo "Building CompanyAsyncQueryFunction..."
	@echo "Current directory: $$(pwd)"
	@echo "Artifacts directory: $(ARTIFACTS_DIR)"
	cd /Users/<USER>/Downloads/secure_double_blind_ec && \
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
	go build -ldflags="-s -w" -o "$(ARTIFACTS_DIR)/bootstrap" lambdas/company-query-async/main.go
	@echo "CompanyAsyncQueryFunction built successfully!"

clean:
	@echo "Cleaning build artifacts..."
	rm -f bootstrap
	@echo "Clean completed!"

# Targets for SAM build
build-CompanyAAsyncQueryFunction: build
build-CompanyBAsyncQueryFunction: build
build-CompanyCAsyncQueryFunction: build
