package main

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/google/uuid"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
)

type QueryRequest struct {
	Identifier string `json:"identifier"` // Plain text identifier to query
	CompanyID  string `json:"company_id"` // This company's ID
}

type QueryResponse struct {
	Success bool      `json:"success"`
	Message string    `json:"message"`
	TaskID  uuid.UUID `json:"task_id"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Parse request body
	var req QueryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.Identifier == "" || req.CompanyID == "" {
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: identifier, company_id")
	}

	// Initialize blinding service for the company
	blindingService, err := company.NewBlindingService(req.CompanyID)
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to initialize blinding service: %v", err))
	}

	// Blind the identifier for query
	blindedPoint, err := blindingService.BlindDataForQuery(req.Identifier)
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to blind identifier: %v", err))
	}

	// Serialize the blinded point
	blindedBytes, err := blindedPoint.MarshalBinary()
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize blinded identifier")
	}

	blindedHex := hex.EncodeToString(blindedBytes)

	// Connect to database
	db, err := database.New()
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	// Create task log entry
	taskID := uuid.New()
	taskLog := &database.TaskLog{
		TaskID:            taskID,
		TaskType:          "query",
		BlindedIdentifier: blindedHex,
		Status:            "pending",
		Response:          make(map[string]interface{}),
		Steps:             []map[string]interface{}{
			{
				"step":        "identifier_blinded",
				"description": "Successfully blinded the identifier for cross-company query",
				"timestamp":   time.Now().Format(time.RFC3339),
				"status":      "completed",
				"data": map[string]interface{}{
					"original_identifier": req.Identifier,
					"company_id":          req.CompanyID,
					"blinded_length":      len(blindedHex),
				},
			},
		},
		CreatedBy: req.CompanyID,
		StartTime: &[]time.Time{time.Now()}[0],
	}

	if err := db.CreateTaskLog(taskLog); err != nil {
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to create task log: %v", err))
	}

	// Add step for task creation
	step := map[string]interface{}{
		"step":        "task_created",
		"description": "Task created successfully, ready for processing",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "completed",
		"data": map[string]interface{}{
			"task_id": taskID.String(),
		},
	}

	if err := db.AddStepToTaskLog(taskID, step); err != nil {
		// Log error but don't fail the request
		fmt.Printf("Failed to add step to task log: %v\n", err)
	}

	// TODO: Here you would typically trigger an async process (SQS, SNS, etc.)
	// For now, we'll just return the task ID and let the polling API handle the processing

	// Prepare response
	response := QueryResponse{
		Success: true,
		Message: "Query task created successfully. Use the task_id to poll for results.",
		TaskID:  taskID,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusCreated,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
