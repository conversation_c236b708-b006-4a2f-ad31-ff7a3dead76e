package main

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/google/uuid"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
)

type QueryRequest struct {
	Identifier string `json:"identifier"` // Plain text identifier to query
	CompanyID  string `json:"company_id"` // This company's ID
}

type QueryResponse struct {
	Success bool      `json:"success"`
	Message string    `json:"message"`
	TaskID  uuid.UUID `json:"task_id"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Handler started: company-query-async")
	log.Printf("Request body: %s", request.Body)
	// Parse request body
	var req QueryRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		log.Printf("JSON unmarshal error: %v", err)
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}
	log.Printf("Parsed request: %+v", req)
	// Validate required fields
	if req.Identifier == "" || req.CompanyID == "" {
		log.Printf("Missing required fields: identifier or company_id")
		return createErrorResponse(http.StatusBadRequest, "Missing required fields: identifier, company_id")
	}
	log.Printf("Initializing blinding service for company: %s", req.CompanyID)
	// Initialize blinding service for the company
	blindingService, err := company.NewBlindingService(req.CompanyID)
	if err != nil {
		log.Printf("Failed to initialize blinding service: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to initialize blinding service: %v", err))
	}
	log.Println("Blinding service initialized successfully")
	// Blind the identifier for query
	blindedPoint, err := blindingService.BlindDataForQuery(req.Identifier)
	if err != nil {
		log.Printf("Failed to blind identifier: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to blind identifier: %v", err))
	}

	// Serialize the blinded point
	blindedBytes, err := blindedPoint.MarshalBinary()
	if err != nil {
		log.Printf("Failed to serialize blinded identifier: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Failed to serialize blinded identifier")
	}
	blindedHex := hex.EncodeToString(blindedBytes)
	log.Printf("Blinded identifier (hex): %s", blindedHex)

	// Connect to database
	db, err := database.New()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	// Create task log entry
	taskID := uuid.New()
	log.Printf("Creating task log entry with taskID: %s", taskID.String())
	taskLog := &database.TaskLog{
		TaskID:            taskID,
		TaskType:          "query",
		BlindedIdentifier: blindedHex,
		Status:            "pending",
		Response:          make(map[string]interface{}),
		Steps:             []map[string]interface{}{
			{
				"step":        "identifier_blinded",
				"description": "Successfully blinded the identifier for cross-company query",
				"timestamp":   time.Now().Format(time.RFC3339),
				"status":      "completed",
				"data": map[string]interface{}{
					"original_identifier": req.Identifier,
					"company_id":          req.CompanyID,
					"blinded_length":      len(blindedHex),
				},
			},
		},
		CreatedBy: req.CompanyID,
		StartTime: &[]time.Time{time.Now()}[0],
	}

	if err := db.CreateTaskLog(taskLog); err != nil {
		log.Printf("Failed to create task log: %v", err)
		return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to create task log: %v", err))
	}
	log.Printf("Task log created and step added for taskID: %s", taskID.String())

	// Add step for task creation
	step := map[string]interface{}{
		"step":        "task_created",
		"description": "Task created successfully, ready for processing",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "completed",
		"data": map[string]interface{}{
			"task_id": taskID.String(),
		},
	}

	if err := db.AddStepToTaskLog(taskID, step); err != nil {
		log.Printf("Failed to add step to task log: %v", err)
	}

	// TODO: Here you would typically trigger an async process (SQS, SNS, etc.)
	// For now, we'll just return the task ID and let the polling API handle the processing

	// Prepare response
	response := QueryResponse{
		Success: true,
		Message: "Query task created successfully. Use the task_id to poll for results.",
		TaskID:  taskID,
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
