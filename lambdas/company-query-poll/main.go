package main

import (
	"bytes"
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/cloudflare/circl/group"
	"github.com/google/uuid"
	"secure_double_blind_ec/internal/company"
	"secure_double_blind_ec/pkg/database"
)

type PollRequest struct {
	TaskID string `json:"task_id"`
}

type PollResponse struct {
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	Status      string                 `json:"status"`
	TaskID      string                 `json:"task_id"`
	Steps       []map[string]interface{} `json:"steps,omitempty"`
	FraudData   []FraudDataResult      `json:"fraud_data,omitempty"`
	Error       *string                `json:"error,omitempty"`
	CompletedAt *string                `json:"completed_at,omitempty"`
}

type FraudDataResult struct {
	ID             int                    `json:"id"`
	Identifier     string                 `json:"identifier"`
	IdentifierType string                 `json:"identifier_type"`
	Metadata       map[string]interface{} `json:"metadata"`
	InstitutionID  int                    `json:"institution_id"`
	CreatedBy      string                 `json:"created_by"`
	Status         string                 `json:"status"`
	FraudType      string                 `json:"fraud_type"`
	CreatedAt      string                 `json:"created_at"`
}

type CentralCrossQueryRequest struct {
	BlindedIdentifier string `json:"blinded_identifier"`
	RequestingCompany string `json:"requesting_company"`
}

type CentralGetDataRequest struct {
	BlindedValues map[string]string `json:"blinded_values"`
}

func handler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Parse request body
	var req PollRequest
	if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
		return createErrorResponse(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.TaskID == "" {
		return createErrorResponse(http.StatusBadRequest, "Missing required field: task_id")
	}

	// Parse UUID
	taskID, err := uuid.Parse(req.TaskID)
	if err != nil {
		return createErrorResponse(http.StatusBadRequest, "Invalid task_id format")
	}

	// Connect to database
	db, err := database.New()
	if err != nil {
		return createErrorResponse(http.StatusInternalServerError, "Database connection failed")
	}
	defer db.Close()

	// Get task log
	taskLog, err := db.GetTaskLogByTaskID(taskID)
	if err != nil {
		return createErrorResponse(http.StatusNotFound, "Task not found")
	}

	// If task is not completed, check if we should process it
	if taskLog.Status == "pending" {
		// Process the task
		if err := processTask(db, taskLog); err != nil {
			// Update task with error
			taskLog.Status = "failed"
			taskLog.Error = &[]string{err.Error()}[0]
			endTime := time.Now()
			taskLog.EndTime = &endTime
			db.UpdateTaskLog(taskLog)
			
			return createErrorResponse(http.StatusInternalServerError, fmt.Sprintf("Task processing failed: %v", err))
		}
	}

	// Prepare response
	response := PollResponse{
		Success: true,
		Message: getMessageForStatus(taskLog.Status),
		Status:  taskLog.Status,
		TaskID:  taskLog.TaskID.String(),
		Steps:   taskLog.Steps,
		Error:   taskLog.Error,
	}

	if taskLog.QueryCompletedAt != nil {
		completedAt := taskLog.QueryCompletedAt.Format(time.RFC3339)
		response.CompletedAt = &completedAt
	}

	// If completed, include fraud data
	if taskLog.Status == "completed" {
		if fraudData, exists := taskLog.Response["fraud_data"]; exists {
			if fraudDataSlice, ok := fraudData.([]interface{}); ok {
				var results []FraudDataResult
				for _, item := range fraudDataSlice {
					if fraudMap, ok := item.(map[string]interface{}); ok {
						result := FraudDataResult{}
						if id, ok := fraudMap["id"].(float64); ok {
							result.ID = int(id)
						}
						if identifier, ok := fraudMap["identifier"].(string); ok {
							result.Identifier = identifier
						}
						if identifierType, ok := fraudMap["identifier_type"].(string); ok {
							result.IdentifierType = identifierType
						}
						if metadata, ok := fraudMap["metadata"].(map[string]interface{}); ok {
							result.Metadata = metadata
						}
						if institutionID, ok := fraudMap["institution_id"].(float64); ok {
							result.InstitutionID = int(institutionID)
						}
						if createdBy, ok := fraudMap["created_by"].(string); ok {
							result.CreatedBy = createdBy
						}
						if status, ok := fraudMap["status"].(string); ok {
							result.Status = status
						}
						if fraudType, ok := fraudMap["fraud_type"].(string); ok {
							result.FraudType = fraudType
						}
						if createdAt, ok := fraudMap["created_at"].(string); ok {
							result.CreatedAt = createdAt
						}
						results = append(results, result)
					}
				}
				response.FraudData = results
			}
		}
	}

	responseJSON, _ := json.Marshal(response)

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func processTask(db *database.DB, taskLog *database.TaskLog) error {
	// Update status to processing
	taskLog.Status = "processing"
	if err := db.UpdateTaskLog(taskLog); err != nil {
		return fmt.Errorf("failed to update task status: %v", err)
	}

	// Add processing step
	step := map[string]interface{}{
		"step":        "processing_started",
		"description": "Started processing the query task",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "completed",
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	// Extract company ID from created_by field
	companyID := taskLog.CreatedBy

	// Initialize blinding service
	blindingService, err := company.NewBlindingService(companyID)
	if err != nil {
		return fmt.Errorf("failed to initialize blinding service: %v", err)
	}

	// Step 1: Call central server to get cross-company blinded values
	step = map[string]interface{}{
		"step":        "calling_central_cross_query",
		"description": "Calling central server to get cross-company blinded values",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "in_progress",
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	crossQueryReq := CentralCrossQueryRequest{
		BlindedIdentifier: taskLog.BlindedIdentifier,
		RequestingCompany: companyID,
	}

	crossQueryResponse, err := callCentralCrossQuery(crossQueryReq)
	if err != nil {
		return fmt.Errorf("failed to get cross-company data: %v", err)
	}

	// Update step as completed
	step["status"] = "completed"
	step["data"] = map[string]interface{}{
		"companies_found": len(crossQueryResponse),
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	// Step 2: Convert response to group elements
	step = map[string]interface{}{
		"step":        "converting_group_elements",
		"description": "Converting API response to group elements for decryption",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "in_progress",
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	groupElements, err := convertResponseToGroupElements(crossQueryResponse)
	if err != nil {
		return fmt.Errorf("failed to convert response format: %v", err)
	}

	step["status"] = "completed"
	db.AddStepToTaskLog(taskLog.TaskID, step)

	// Step 3: Decrypt the double-encrypted values
	step = map[string]interface{}{
		"step":        "decrypting_values",
		"description": "Decrypting double-encrypted values to get other companies' blinded values",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "in_progress",
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	decryptedValues, err := blindingService.DecryptDoubleEncryptedValues(groupElements)
	if err != nil {
		return fmt.Errorf("failed to decrypt cross-company values: %v", err)
	}

	step["status"] = "completed"
	step["data"] = map[string]interface{}{
		"decrypted_companies": len(decryptedValues),
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	// Step 4: Call central server to get fraud data
	step = map[string]interface{}{
		"step":        "fetching_fraud_data",
		"description": "Fetching fraud data from central server using decrypted blinded values",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "in_progress",
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	getDataReq := CentralGetDataRequest{
		BlindedValues: decryptedValues,
	}

	fraudData, err := callCentralGetData(getDataReq)
	if err != nil {
		return fmt.Errorf("failed to get fraud data: %v", err)
	}

	step["status"] = "completed"
	step["data"] = map[string]interface{}{
		"fraud_records_found": len(fraudData),
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	// Update task as completed
	now := time.Now()
	taskLog.Status = "completed"
	taskLog.QueryCompletedAt = &now
	taskLog.EndTime = &now
	taskLog.Response = map[string]interface{}{
		"fraud_data": fraudData,
	}

	if err := db.UpdateTaskLog(taskLog); err != nil {
		return fmt.Errorf("failed to update task completion: %v", err)
	}

	// Add final completion step
	step = map[string]interface{}{
		"step":        "query_completed",
		"description": "Query completed successfully",
		"timestamp":   time.Now().Format(time.RFC3339),
		"status":      "completed",
		"data": map[string]interface{}{
			"total_fraud_records": len(fraudData),
			"processing_time":     now.Sub(*taskLog.StartTime).String(),
		},
	}
	db.AddStepToTaskLog(taskLog.TaskID, step)

	return nil
}

func callCentralCrossQuery(req CentralCrossQueryRequest) (map[string]interface{}, error) {
	centralServerURL := os.Getenv("CENTRAL_SERVER_URL")
	if centralServerURL == "" {
		centralServerURL = "https://cd8bofmqb0.execute-api.ap-south-1.amazonaws.com/dev"
	}

	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(centralServerURL+"/central/cross-query", "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to call central cross-query: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("central server returned status: %d", resp.StatusCode)
	}

	var response map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	// Extract the blinded_values from the response
	if blindedValues, ok := response["blinded_values"].(map[string]interface{}); ok {
		result := make(map[string]interface{})
		for k, v := range blindedValues {
			if strVal, ok := v.(string); ok {
				result[k] = strVal
			}
		}
		return result, nil
	}

	return nil, fmt.Errorf("invalid response format from central server")
}

func callCentralGetData(req CentralGetDataRequest) ([]FraudDataResult, error) {
	centralServerURL := os.Getenv("CENTRAL_SERVER_URL")
	if centralServerURL == "" {
		centralServerURL = "https://cd8bofmqb0.execute-api.ap-south-1.amazonaws.com/dev"
	}

	requestBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	resp, err := http.Post(centralServerURL+"/central/get-data", "application/json", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to call central get-data: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("central server returned status: %d", resp.StatusCode)
	}

	var response struct {
		Success   bool              `json:"success"`
		FraudData []FraudDataResult `json:"fraud_data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %v", err)
	}

	return response.FraudData, nil
}

func convertResponseToGroupElements(response map[string]interface{}) (map[string]group.Element, error) {
	result := make(map[string]group.Element)
	g := group.P256

	for companyID, value := range response {
		strValue, ok := value.(string)
		if !ok {
			return nil, fmt.Errorf("expected string value for company %s, got %T", companyID, value)
		}

		bytes, err := hex.DecodeString(strValue)
		if err != nil {
			return nil, fmt.Errorf("failed to decode hex for company %s: %v", companyID, err)
		}

		element := g.NewElement()
		if err := element.UnmarshalBinary(bytes); err != nil {
			return nil, fmt.Errorf("failed to unmarshal group element for company %s: %v", companyID, err)
		}

		result[companyID] = element
	}

	return result, nil
}

func getMessageForStatus(status string) string {
	switch status {
	case "pending":
		return "Task is pending processing"
	case "processing":
		return "Task is currently being processed"
	case "completed":
		return "Task completed successfully"
	case "failed":
		return "Task failed during processing"
	default:
		return "Unknown task status"
	}
}

func createErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
	errorResponse := map[string]interface{}{
		"success": false,
		"error":   message,
	}

	responseJSON, _ := json.Marshal(errorResponse)

	return events.APIGatewayProxyResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type":                 "application/json",
			"Access-Control-Allow-Origin":  "*",
			"Access-Control-Allow-Methods": "POST, OPTIONS",
			"Access-Control-Allow-Headers": "Content-Type, Authorization",
		},
		Body: string(responseJSON),
	}, nil
}

func main() {
	lambda.Start(handler)
}
