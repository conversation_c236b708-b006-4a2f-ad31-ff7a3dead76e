#!/bin/bash

# Test script for the new async query system
# This script demonstrates how to use the new async query and polling APIs

set -e

# Configuration
COMPANY_A_BASE_URL="https://bx7ztelqn4.execute-api.ap-south-1.amazonaws.com/dev"
COMPANY_B_BASE_URL="https://556sf0sbw5.execute-api.ap-south-1.amazonaws.com/dev"
COMPANY_C_BASE_URL="https://31e9j4fkc0.execute-api.ap-south-1.amazonaws.com/dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Testing Async Query System ===${NC}"

# Function to test async query
test_async_query() {
    local company_url=$1
    local company_name=$2
    local identifier=$3
    
    echo -e "${YELLOW}Testing async query for $company_name...${NC}"
    
    # Step 1: Submit async query
    echo "Step 1: Submitting async query..."
    response=$(curl -s -X POST "$company_url/query-async" \
        -H "Content-Type: application/json" \
        -d "{
            \"identifier\": \"$identifier\",
            \"company_id\": \"$company_name\"
        }")
    
    echo "Response: $response"
    
    # Extract task_id from response
    task_id=$(echo $response | jq -r '.task_id')
    
    if [ "$task_id" == "null" ]; then
        echo -e "${RED}Failed to get task_id from response${NC}"
        return 1
    fi
    
    echo -e "${GREEN}Task created with ID: $task_id${NC}"
    
    # Step 2: Poll for results
    echo "Step 2: Polling for results..."
    max_attempts=10
    attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        echo "Polling attempt $((attempt + 1))..."
        
        poll_response=$(curl -s -X POST "$company_url/poll" \
            -H "Content-Type: application/json" \
            -d "{
                \"task_id\": \"$task_id\"
            }")
        
        status=$(echo $poll_response | jq -r '.status')
        echo "Current status: $status"
        
        if [ "$status" == "completed" ]; then
            echo -e "${GREEN}Query completed successfully!${NC}"
            echo "Final response: $poll_response"
            
            # Show fraud data if any
            fraud_data=$(echo $poll_response | jq '.fraud_data')
            if [ "$fraud_data" != "null" ] && [ "$fraud_data" != "[]" ]; then
                echo -e "${GREEN}Fraud data found:${NC}"
                echo $fraud_data | jq '.'
            else
                echo -e "${YELLOW}No fraud data found for this identifier${NC}"
            fi
            
            # Show processing steps
            echo -e "${GREEN}Processing steps:${NC}"
            echo $poll_response | jq '.steps[]'
            
            return 0
        elif [ "$status" == "failed" ]; then
            echo -e "${RED}Query failed!${NC}"
            error=$(echo $poll_response | jq -r '.error')
            echo "Error: $error"
            return 1
        else
            echo "Status: $status - waiting 2 seconds..."
            sleep 2
        fi
        
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}Timeout waiting for query completion${NC}"
    return 1
}

# Test cases
echo -e "${GREEN}Test Case 1: Company A <NAME_EMAIL>${NC}"
test_async_query "$COMPANY_A_BASE_URL" "company_a" "<EMAIL>"

echo ""
echo -e "${GREEN}Test Case 2: Company B <NAME_EMAIL>${NC}"
test_async_query "$COMPANY_B_BASE_URL" "company_b" "<EMAIL>"

echo ""
echo -e "${GREEN}Test Case 3: Company C <NAME_EMAIL>${NC}"
test_async_query "$COMPANY_C_BASE_URL" "company_c" "<EMAIL>"

echo ""
echo -e "${GREEN}=== Async Query System Test Complete ===${NC}"
