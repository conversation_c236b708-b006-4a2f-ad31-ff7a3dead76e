package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/google/uuid"
	_ "github.com/lib/pq"
)

type DB struct {
	conn *sql.DB
}

// Institution represents the institution table
type Institution struct {
	ID              int       `json:"id"`
	InstitutionName string    `json:"institution_name"`
	AccessRole      string    `json:"access_role"`
	CreatedBy       string    `json:"created_by"`
	UpdatedBy       *string   `json:"updated_by"`
	Status          string    `json:"status"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// FraudData represents the fraud_data table
type FraudData struct {
	ID             int                    `json:"id"`
	Identifier     string                 `json:"identifier"`
	IdentifierType string                 `json:"identifier_type"`
	Metadata       map[string]interface{} `json:"metadata"`
	InstitutionID  int                    `json:"institution_id"`
	CreatedBy      string                 `json:"created_by"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	Status         string                 `json:"status"`
	FraudType      string                 `json:"fraud_type"`
	UpdatedBy      *string                `json:"updated_by"`
	AssociationID  *int                   `json:"association_id"`
}

// IDAssociation represents the id_associations table
type IDAssociation struct {
	ID           int                    `json:"id"`
	Associations map[string]interface{} `json:"associations"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
}

// AuditLog represents the audit_logs table
type AuditLog struct {
	ID            int                    `json:"id"`
	EventType     string                 `json:"event_type"`
	InstitutionID *int                   `json:"institution_id"`
	UserID        string                 `json:"user_id"`
	Metadata      map[string]interface{} `json:"metadata"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// TaskLog represents the task_log table
type TaskLog struct {
	ID                 int                      `json:"id"`
	TaskID             uuid.UUID                `json:"task_id"`
	TaskType           string                   `json:"task_type"`
	BlindedIdentifier  string                   `json:"blinded_identifier"`
	Status             string                   `json:"status"`
	Response           map[string]interface{}   `json:"response"`
	Steps              []map[string]interface{} `json:"steps"`
	Notes              *string                  `json:"notes"`
	Error              *string                  `json:"error"`
	CreatedAt          time.Time                `json:"created_at"`
	UpdatedAt          time.Time                `json:"updated_at"`
	CreatedBy          string                   `json:"created_by"`
	QueryCompletedAt   *time.Time               `json:"query_completed_at"`
	StartTime          *time.Time               `json:"start_time"`
	EndTime            *time.Time               `json:"end_time"`
}

// New creates a new database connection
func New() (*DB, error) {
	host := os.Getenv("DB_HOST")
	port := os.Getenv("DB_PORT")
	user := os.Getenv("DB_USERNAME")
	password := os.Getenv("DB_PASSWORD")
	dbname := os.Getenv("DB_NAME")
	schema := os.Getenv("DB_SCHEMA")

	if host == "" {
		host = "localhost"
	}
	if port == "" {
		port = "5432"
	}
	if schema == "" {
		schema = "consortium"
	}

	// For external databases, use sslmode=require instead of prefer
	psqlInfo := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=require search_path=%s",
		host, port, user, password, dbname, schema)

	db, err := sql.Open("postgres", psqlInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %v", err)
	}

	if err = db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %v", err)
	}

	// Ensure the schema exists and set search_path
	_, err = db.Exec(fmt.Sprintf("CREATE SCHEMA IF NOT EXISTS %s", schema))
	if err != nil {
		return nil, fmt.Errorf("failed to create schema: %v", err)
	}

	_, err = db.Exec(fmt.Sprintf("SET search_path TO %s", schema))
	if err != nil {
		return nil, fmt.Errorf("failed to set search_path: %v", err)
	}

	return &DB{conn: db}, nil
}

// Close closes the database connection
func (db *DB) Close() error {
	return db.conn.Close()
}

// CreateInstitution creates a new institution
func (db *DB) CreateInstitution(inst *Institution) error {
	query := `
		INSERT INTO institution (institution_name, access_role, created_by, status)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at, updated_at`

	err := db.conn.QueryRow(query, inst.InstitutionName, inst.AccessRole, inst.CreatedBy, inst.Status).
		Scan(&inst.ID, &inst.CreatedAt, &inst.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create institution: %v", err)
	}

	return nil
}

// GetInstitutionByName gets an institution by name
func (db *DB) GetInstitutionByName(name string) (*Institution, error) {
	inst := &Institution{}
	query := `
		SELECT id, institution_name, access_role, created_by, updated_by, status, created_at, updated_at
		FROM institution
		WHERE institution_name = $1`

	err := db.conn.QueryRow(query, name).Scan(
		&inst.ID, &inst.InstitutionName, &inst.AccessRole, &inst.CreatedBy,
		&inst.UpdatedBy, &inst.Status, &inst.CreatedAt, &inst.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to get institution: %v", err)
	}

	return inst, nil
}

// CreateFraudData creates a new fraud data record
func (db *DB) CreateFraudData(fraud *FraudData) error {
	metadataJSON, err := json.Marshal(fraud.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %v", err)
	}

	query := `
		INSERT INTO fraud_data (identifier, identifier_type, metadata, institution_id, created_by, status, fraud_type, association_id)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
		RETURNING id, created_at, updated_at`

	err = db.conn.QueryRow(query,
		fraud.Identifier, fraud.IdentifierType, metadataJSON, fraud.InstitutionID,
		fraud.CreatedBy, fraud.Status, fraud.FraudType, fraud.AssociationID).
		Scan(&fraud.ID, &fraud.CreatedAt, &fraud.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create fraud data: %v", err)
	}

	return nil
}

// GetFraudDataByIdentifier gets fraud data by identifier
func (db *DB) GetFraudDataByIdentifier(identifier string) ([]*FraudData, error) {
	query := `
		SELECT id, identifier, identifier_type, metadata, institution_id, created_by, 
		       created_at, updated_at, status, fraud_type, updated_by, association_id
		FROM fraud_data
		WHERE identifier = $1`

	rows, err := db.conn.Query(query, identifier)
	if err != nil {
		return nil, fmt.Errorf("failed to query fraud data: %v", err)
	}
	defer rows.Close()

	var results []*FraudData
	for rows.Next() {
		fraud := &FraudData{}
		var metadataJSON []byte

		err := rows.Scan(
			&fraud.ID, &fraud.Identifier, &fraud.IdentifierType, &metadataJSON,
			&fraud.InstitutionID, &fraud.CreatedBy, &fraud.CreatedAt, &fraud.UpdatedAt,
			&fraud.Status, &fraud.FraudType, &fraud.UpdatedBy, &fraud.AssociationID)

		if err != nil {
			return nil, fmt.Errorf("failed to scan fraud data: %v", err)
		}

		if err := json.Unmarshal(metadataJSON, &fraud.Metadata); err != nil {
			return nil, fmt.Errorf("failed to unmarshal metadata: %v", err)
		}

		results = append(results, fraud)
	}

	return results, nil
}

// CreateIDAssociation creates a new ID association
func (db *DB) CreateIDAssociation(assoc *IDAssociation) error {
	associationsJSON, err := json.Marshal(assoc.Associations)
	if err != nil {
		return fmt.Errorf("failed to marshal associations: %v", err)
	}

	query := `
		INSERT INTO id_associations (associations)
		VALUES ($1)
		RETURNING id, created_at, updated_at`

	err = db.conn.QueryRow(query, associationsJSON).
		Scan(&assoc.ID, &assoc.CreatedAt, &assoc.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create ID association: %v", err)
	}

	return nil
}

// CreateAuditLog creates a new audit log entry
func (db *DB) CreateAuditLog(log *AuditLog) error {
	metadataJSON, err := json.Marshal(log.Metadata)
	if err != nil {
		return fmt.Errorf("failed to marshal metadata: %v", err)
	}

	query := `
		INSERT INTO audit_logs (event_type, institution_id, user_id, metadata)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at, updated_at`

	err = db.conn.QueryRow(query, log.EventType, log.InstitutionID, log.UserID, metadataJSON).
		Scan(&log.ID, &log.CreatedAt, &log.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create audit log: %v", err)
	}

	return nil
}

// GetFraudDataByAssociationID gets fraud data by association ID
func (db *DB) GetFraudDataByAssociationID(associationID int) ([]*FraudData, error) {
	query := `
		SELECT id, identifier, identifier_type, metadata, institution_id, created_by, 
		       created_at, updated_at, status, fraud_type, updated_by, association_id
		FROM fraud_data
		WHERE association_id = $1`

	rows, err := db.conn.Query(query, associationID)
	if err != nil {
		return nil, fmt.Errorf("failed to query fraud data by association ID: %v", err)
	}
	defer rows.Close()

	var results []*FraudData
	for rows.Next() {
		fraud := &FraudData{}
		var metadataJSON []byte

		err := rows.Scan(
			&fraud.ID, &fraud.Identifier, &fraud.IdentifierType, &metadataJSON,
			&fraud.InstitutionID, &fraud.CreatedBy, &fraud.CreatedAt, &fraud.UpdatedAt,
			&fraud.Status, &fraud.FraudType, &fraud.UpdatedBy, &fraud.AssociationID)

		if err != nil {
			return nil, fmt.Errorf("failed to scan fraud data: %v", err)
		}

		if err := json.Unmarshal(metadataJSON, &fraud.Metadata); err != nil {
			return nil, fmt.Errorf("failed to unmarshal metadata: %v", err)
		}

		results = append(results, fraud)
	}

	return results, nil
}

// CreateTaskLog creates a new task log entry
func (db *DB) CreateTaskLog(taskLog *TaskLog) error {
	responseJSON, err := json.Marshal(taskLog.Response)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %v", err)
	}

	stepsJSON, err := json.Marshal(taskLog.Steps)
	if err != nil {
		return fmt.Errorf("failed to marshal steps: %v", err)
	}

	query := `
		INSERT INTO task_log (task_id, task_type, blinded_identifier, status, response, steps, notes, error, created_by, start_time)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING id, created_at, updated_at`

	err = db.conn.QueryRow(query,
		taskLog.TaskID, taskLog.TaskType, taskLog.BlindedIdentifier, taskLog.Status,
		responseJSON, stepsJSON, taskLog.Notes, taskLog.Error, taskLog.CreatedBy, taskLog.StartTime).
		Scan(&taskLog.ID, &taskLog.CreatedAt, &taskLog.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create task log: %v", err)
	}

	return nil
}

// GetTaskLogByTaskID gets a task log by task ID
func (db *DB) GetTaskLogByTaskID(taskID uuid.UUID) (*TaskLog, error) {
	taskLog := &TaskLog{}
	var responseJSON, stepsJSON []byte

	query := `
		SELECT id, task_id, task_type, blinded_identifier, status, response, steps, notes, error, 
		       created_at, updated_at, created_by, query_completed_at, start_time, end_time
		FROM task_log
		WHERE task_id = $1`

	err := db.conn.QueryRow(query, taskID).Scan(
		&taskLog.ID, &taskLog.TaskID, &taskLog.TaskType, &taskLog.BlindedIdentifier,
		&taskLog.Status, &responseJSON, &stepsJSON, &taskLog.Notes, &taskLog.Error,
		&taskLog.CreatedAt, &taskLog.UpdatedAt, &taskLog.CreatedBy,
		&taskLog.QueryCompletedAt, &taskLog.StartTime, &taskLog.EndTime)

	if err != nil {
		return nil, fmt.Errorf("failed to get task log: %v", err)
	}

	if err := json.Unmarshal(responseJSON, &taskLog.Response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	if err := json.Unmarshal(stepsJSON, &taskLog.Steps); err != nil {
		return nil, fmt.Errorf("failed to unmarshal steps: %v", err)
	}

	return taskLog, nil
}

// UpdateTaskLog updates an existing task log
func (db *DB) UpdateTaskLog(taskLog *TaskLog) error {
	responseJSON, err := json.Marshal(taskLog.Response)
	if err != nil {
		return fmt.Errorf("failed to marshal response: %v", err)
	}

	stepsJSON, err := json.Marshal(taskLog.Steps)
	if err != nil {
		return fmt.Errorf("failed to marshal steps: %v", err)
	}

	query := `
		UPDATE task_log 
		SET status = $1, response = $2, steps = $3, notes = $4, error = $5, 
		    query_completed_at = $6, end_time = $7
		WHERE task_id = $8`

	_, err = db.conn.Exec(query,
		taskLog.Status, responseJSON, stepsJSON, taskLog.Notes, taskLog.Error,
		taskLog.QueryCompletedAt, taskLog.EndTime, taskLog.TaskID)

	if err != nil {
		return fmt.Errorf("failed to update task log: %v", err)
	}

	return nil
}

// AddStepToTaskLog adds a new step to an existing task log
func (db *DB) AddStepToTaskLog(taskID uuid.UUID, step map[string]interface{}) error {
	query := `
		UPDATE task_log 
		SET steps = steps || $1::jsonb
		WHERE task_id = $2`

	stepJSON, err := json.Marshal([]map[string]interface{}{step})
	if err != nil {
		return fmt.Errorf("failed to marshal step: %v", err)
	}

	_, err = db.conn.Exec(query, stepJSON, taskID)
	if err != nil {
		return fmt.Errorf("failed to add step to task log: %v", err)
	}

	return nil
}
