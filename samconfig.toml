version = 0.1

[default.deploy.parameters]
stack_name = "secure-double-blind-ec"
resolve_s3 = true
s3_prefix = "secure-double-blind-ec"
region = "ap-south-1"
confirm_changeset = true
capabilities = "CAPABILITY_IAM"
parameter_overrides = "Environment=\"dev\" DatabaseHost=\"modus-db-dev.cvui26wus1f1.ap-south-1.rds.amazonaws.com\" DatabasePort=\"5432\" DatabaseUsername=\"postgres\" DatabasePassword=\"1rpraZEuRTp9aJjzFX47\" DatabaseName=\"consortium\" CompanyAKey=\"0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef\" CompanyASalt=\"fedcba9876543210fedcba9876543210fedcba9876543210fedcba9876543210\" CompanyBKey=\"abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789\" CompanyBSalt=\"9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba\" CompanyCKey=\"56789abcdef012356789abcdef012356789abcdef012356789abcdef01234567\" CompanyCSalt=\"210fedcba9876543210fedcba9876543210fedcba9876543210fedcba9876543\""
image_repositories = []
