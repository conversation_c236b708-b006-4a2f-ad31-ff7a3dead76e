-- Database schema for the secure double blind EC system

-- Create consortium schema if not exists
CREATE SCHEMA IF NOT EXISTS consortium;

-- Set search path to consortium schema
SET search_path TO consortium;

-- Institution table
CREATE TABLE IF NOT EXISTS institution (
    id SERIAL PRIMARY KEY,
    institution_name VARCHAR(255) NOT NULL UNIQUE,
    access_role VARCHAR(100) NOT NULL,
    created_by VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_by <PERSON><PERSON><PERSON><PERSON>(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on institution_name for faster lookups
CREATE INDEX IF NOT EXISTS idx_institution_name ON institution(institution_name);
CREATE INDEX IF NOT EXISTS idx_institution_status ON institution(status);

-- ID Associations table
CREATE TABLE IF NOT EXISTS id_associations (
    id SERIAL PRIMARY KEY,
    associations JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on associations JSONB column
CREATE INDEX IF NOT EXISTS idx_id_associations_gin ON id_associations USING GIN(associations);

-- Fraud data table
CREATE TABLE IF NOT EXISTS fraud_data (
    id SERIAL PRIMARY KEY,
    identifier TEXT NOT NULL,
    identifier_type VARCHAR(50) NOT NULL,
    metadata JSONB,
    institution_id INTEGER NOT NULL REFERENCES institution(id) ON DELETE CASCADE,
    created_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) NOT NULL CHECK (status IN ('Confirmed', 'Suspected', 'Absolved')),
    fraud_type VARCHAR(50) NOT NULL CHECK (fraud_type IN ('Mule', 'Identity Theft', 'Fraud')),
    updated_by VARCHAR(255),
    association_id INTEGER REFERENCES id_associations(id) ON DELETE SET NULL
);

-- Create indexes for fraud_data table
CREATE INDEX IF NOT EXISTS idx_fraud_data_identifier ON fraud_data(identifier);
CREATE INDEX IF NOT EXISTS idx_fraud_data_institution_id ON fraud_data(institution_id);
CREATE INDEX IF NOT EXISTS idx_fraud_data_status ON fraud_data(status);
CREATE INDEX IF NOT EXISTS idx_fraud_data_fraud_type ON fraud_data(fraud_type);
CREATE INDEX IF NOT EXISTS idx_fraud_data_identifier_type ON fraud_data(identifier_type);
CREATE INDEX IF NOT EXISTS idx_fraud_data_created_at ON fraud_data(created_at);
CREATE INDEX IF NOT EXISTS idx_fraud_data_association_id ON fraud_data(association_id);

-- Create GIN index on metadata JSONB column
CREATE INDEX IF NOT EXISTS idx_fraud_data_metadata_gin ON fraud_data USING GIN(metadata);

-- Audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(100) NOT NULL,
    institution_id INTEGER REFERENCES institution(id) ON DELETE SET NULL,
    user_id VARCHAR(255) NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for audit_logs table
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_institution_id ON audit_logs(institution_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- Create GIN index on metadata JSONB column
CREATE INDEX IF NOT EXISTS idx_audit_logs_metadata_gin ON audit_logs USING GIN(metadata);

-- Task log table for tracking asynchronous query operations
CREATE TABLE IF NOT EXISTS task_log (
    id SERIAL PRIMARY KEY,
    task_id UUID NOT NULL UNIQUE,
    task_type VARCHAR(50) NOT NULL DEFAULT 'query',
    blinded_identifier TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    response JSONB,
    steps JSONB DEFAULT '[]'::jsonb,
    notes TEXT,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    query_completed_at TIMESTAMP WITH TIME ZONE,
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE
);

-- Create indexes for task_log table
CREATE INDEX IF NOT EXISTS idx_task_log_task_id ON task_log(task_id);
CREATE INDEX IF NOT EXISTS idx_task_log_status ON task_log(status);
CREATE INDEX IF NOT EXISTS idx_task_log_task_type ON task_log(task_type);
CREATE INDEX IF NOT EXISTS idx_task_log_created_by ON task_log(created_by);
CREATE INDEX IF NOT EXISTS idx_task_log_created_at ON task_log(created_at);

-- Create GIN indexes on JSONB columns
CREATE INDEX IF NOT EXISTS idx_task_log_response_gin ON task_log USING GIN(response);
CREATE INDEX IF NOT EXISTS idx_task_log_steps_gin ON task_log USING GIN(steps);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic updated_at updates
CREATE TRIGGER update_institution_updated_at 
    BEFORE UPDATE ON institution 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fraud_data_updated_at 
    BEFORE UPDATE ON fraud_data 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_id_associations_updated_at 
    BEFORE UPDATE ON id_associations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_audit_logs_updated_at 
    BEFORE UPDATE ON audit_logs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_log_updated_at 
    BEFORE UPDATE ON task_log 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default institutions for testing
INSERT INTO institution (institution_name, access_role, created_by, status) 
VALUES 
    ('Central Authority', 'admin', 'system', 'active'),
    ('Company A', 'company', 'system', 'active'),
    ('Company B', 'company', 'system', 'active'),
    ('Company C', 'company', 'system', 'active')
ON CONFLICT (institution_name) DO NOTHING;
